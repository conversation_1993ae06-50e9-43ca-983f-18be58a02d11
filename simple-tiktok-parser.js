/**
 * 简化版TikTok视频解析测试脚本
 * 仅使用Node.js内置模块，无需安装额外依赖
 * 
 * 使用方法:
 * node simple-tiktok-parser.js
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');
const readline = require('readline');

// 配置
const CONFIG = {
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
  timeout: 15000,
  maxRetries: 3
};

/**
 * 发起HTTP/HTTPS请求 (支持POST)
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const method = options.method || 'GET';
    const body = options.body || null;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        'User-Agent': CONFIG.userAgent,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Accept-Encoding': 'gzip, deflate',
        'DNT': '1',
        'Connection': 'close',
        'Upgrade-Insecure-Requests': '1',
        ...options.headers
      },
      timeout: CONFIG.timeout
    };

    // 如果有请求体，设置Content-Length
    if (body) {
      requestOptions.headers['Content-Length'] = Buffer.byteLength(body);
    }

    const req = client.request(requestOptions, (res) => {
      let data = '';

      // 处理重定向
      if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: ''
        });
        return;
      }

      res.setEncoding('utf8');

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    // 写入请求体
    if (body) {
      req.write(body);
    }

    req.end();
  });
}

/**
 * 提取TikTok视频ID
 */
function extractVideoId(url) {
  console.log('🔍 提取视频ID:', url);
  
  const patterns = [
    /\/video\/(\d+)/,
    /\/photo\/(\d+)/,
    /\/t\/([A-Za-z0-9]+)/,
    /vm\.tiktok\.com\/([A-Za-z0-9]+)/,
    /vt\.tiktok\.com\/([A-Za-z0-9]+)/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      let videoId = match[1];
      // 如果ID太长，可能包含查询参数，需要截取
      if (videoId.length > 19) {
        const questionIndex = videoId.indexOf('?');
        if (questionIndex > 0) {
          videoId = videoId.substring(0, questionIndex);
        }
      }
      console.log('✅ 找到视频ID:', videoId);
      return videoId;
    }
  }
  
  console.log('❌ 无法提取视频ID');
  return null;
}

/**
 * 处理短链接重定向
 */
async function resolveShortUrl(url) {
  if (!url.includes('vm.tiktok.com') && !url.includes('vt.tiktok.com')) {
    return url;
  }
  
  console.log('🔄 处理短链接重定向...');
  
  try {
    const response = await makeRequest(url, { method: 'HEAD' });
    
    if (response.statusCode >= 300 && response.statusCode < 400) {
      const location = response.headers.location;
      if (location) {
        console.log('✅ 重定向到:', location);
        return location;
      }
    }
  } catch (error) {
    console.log('⚠️ 重定向失败，使用原URL:', error.message);
  }
  
  return url;
}

/**
 * 使用TikTok API解析 (2024-2025最新方法)
 */
async function parseWithAPI(videoId) {
  console.log('📡 尝试TikTok API解析...');

  // 尝试多个API端点
  const apiEndpoints = [
    // 新版API端点
    `https://api16-normal-c-useast1a.tiktokv.com/aweme/v1/feed/?aweme_id=${videoId}&iid=7318518857994389254&device_id=7318517321748022790&channel=googleplay&app_name=musical_ly&version_code=300904&device_platform=android&device_type=ASUS_Z01QD&version=9`,
    // 备用API端点
    `https://api22-normal-c-alisg.tiktokv.com/aweme/v1/feed/?aweme_id=${videoId}&iid=7318518857994389254&device_id=7318517321748022790&channel=googleplay&app_name=musical_ly&version_code=300904&device_platform=android&device_type=ASUS_Z01QD&version=9`,
    // 移动端API
    `https://m.tiktok.com/api/item/detail/?itemId=${videoId}`,
    // Web API
    `https://www.tiktok.com/api/item/detail/?itemId=${videoId}`
  ];

  for (const apiUrl of apiEndpoints) {
    try {
      console.log('🔄 尝试API端点:', apiUrl.split('?')[0]);

      const response = await makeRequest(apiUrl, {
        headers: {
          'X-Requested-With': 'XMLHttpRequest',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });

      if (response.statusCode !== 200) {
        console.log(`⚠️ API响应状态: ${response.statusCode}`);
        continue;
      }

      let data;
      try {
        data = JSON.parse(response.body);
      } catch (e) {
        console.log('⚠️ JSON解析失败，尝试下一个端点');
        continue;
      }

      // 处理不同API格式的响应
      let video = null;

      if (data.aweme_list && data.aweme_list.length > 0) {
        // 标准aweme API格式
        video = data.aweme_list[0];
      } else if (data.itemInfo && data.itemInfo.itemStruct) {
        // Web API格式
        video = data.itemInfo.itemStruct;
      } else if (data.data && data.data.item) {
        // 移动端API格式
        video = data.data.item;
      }

      if (video) {
        const result = {
          method: 'TikTok API',
          success: true,
          data: {
            id: video.aweme_id || video.id || videoId,
            desc: video.desc || video.description || '',
            author: {
              unique_id: video.author?.unique_id || video.author?.uniqueId || '',
              nickname: video.author?.nickname || video.author?.nickname || '',
              avatar: video.author?.avatar_thumb?.url_list?.[0] || video.author?.avatarThumb || ''
            },
            video: {
              // 无水印链接 (play_addr)
              no_watermark: video.video?.play_addr?.url_list?.[0] || video.video?.playAddr || '',
              // 有水印链接 (download_addr)
              with_watermark: video.video?.download_addr?.url_list?.[0] || video.video?.downloadAddr || '',
              cover: video.video?.cover?.url_list?.[0] || video.video?.cover || '',
              duration: video.video?.duration || 0
            },
            // 图集模式
            images: video.image_post_info?.images?.map(img =>
              img.display_image?.url_list?.[1] || img.display_image?.url_list?.[0]
            ) || video.imagePost?.images?.map(img => img.imageURL?.urlList?.[0]) || [],
            stats: {
              digg_count: video.statistics?.digg_count || video.stats?.diggCount || 0,
              comment_count: video.statistics?.comment_count || video.stats?.commentCount || 0,
              share_count: video.statistics?.share_count || video.stats?.shareCount || 0,
              play_count: video.statistics?.play_count || video.stats?.playCount || 0
            }
          }
        };

        console.log('✅ API解析成功');
        return result;
      }

    } catch (error) {
      console.log(`⚠️ API端点失败: ${error.message}`);
      continue;
    }
  }

  console.log('❌ 所有API端点都失败了');
  return { method: 'TikTok API', success: false, error: '所有API端点都失败了' };
}

/**
 * 网页解析方法 (增强版)
 */
async function parseWithWebScraping(url) {
  console.log('🌐 尝试网页解析...');

  try {
    const response = await makeRequest(url);

    if (response.statusCode !== 200) {
      throw new Error(`HTTP ${response.statusCode}`);
    }

    const html = response.body;

    // 简单的正则提取（不使用cheerio）
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
    const title = titleMatch ? titleMatch[1].replace(/\s*\|\s*TikTok$/, '').trim() : '';

    const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)/i);
    const description = descMatch ? descMatch[1] : '';

    const videoUrlMatch = html.match(/<meta[^>]*property=["']og:video["'][^>]*content=["']([^"']*)/i);
    const videoUrl = videoUrlMatch ? videoUrlMatch[1] : '';

    const imageMatch = html.match(/<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']*)/i);
    const image = imageMatch ? imageMatch[1] : '';

    // 尝试提取JSON数据 - 多种模式
    const jsonPatterns = [
      /__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*({.+?})\s*<\/script>/s,
      /window\.__INITIAL_STATE__\s*=\s*({.+?});/s,
      /SIGI_STATE\s*=\s*({.+?});/s,
      /"ItemModule":\s*({.+?})/s
    ];

    let jsonData = null;
    let extractedVideoData = null;

    for (const pattern of jsonPatterns) {
      const jsonMatch = html.match(pattern);
      if (jsonMatch) {
        try {
          jsonData = JSON.parse(jsonMatch[1]);
          console.log('✅ 找到JSON数据');

          // 尝试从JSON中提取视频信息
          extractedVideoData = extractVideoFromJson(jsonData);
          if (extractedVideoData) {
            console.log('✅ 从JSON中提取到视频数据');
            break;
          }
        } catch (e) {
          console.log('⚠️ JSON解析失败，尝试下一个模式');
          continue;
        }
      }
    }

    // 如果从JSON中提取到了完整数据，返回详细信息
    if (extractedVideoData) {
      return {
        method: 'Web Scraping (Enhanced)',
        success: true,
        data: extractedVideoData
      };
    }

    // 否则返回基本信息
    if (title || videoUrl || jsonData) {
      console.log('✅ 网页解析成功（基本模式）');
      return {
        method: 'Web Scraping',
        success: true,
        data: {
          title: title,
          description: description,
          video: {
            url: videoUrl,
            cover: image
          },
          jsonData: jsonData ? '已提取' : '未找到'
        }
      };
    }

    throw new Error('无法从网页中提取有效信息');

  } catch (error) {
    console.log('❌ 网页解析失败:', error.message);
    return { method: 'Web Scraping', success: false, error: error.message };
  }
}

/**
 * 从JSON数据中提取视频信息
 */
function extractVideoFromJson(jsonData) {
  try {
    // 尝试不同的JSON结构路径
    const paths = [
      'default.webapp.video-detail.itemInfo.itemStruct',
      'default.ItemModule',
      'ItemModule',
      'webapp.video-detail.itemInfo.itemStruct'
    ];

    for (const path of paths) {
      let current = jsonData;
      const parts = path.split('.');

      for (const part of parts) {
        if (current && current[part]) {
          current = current[part];
        } else {
          current = null;
          break;
        }
      }

      if (current && (current.id || current.aweme_id)) {
        const video = current;

        return {
          id: video.id || video.aweme_id,
          desc: video.desc || video.description || '',
          author: {
            unique_id: video.author?.uniqueId || video.author?.unique_id || '',
            nickname: video.author?.nickname || '',
            avatar: video.author?.avatarThumb || video.author?.avatar_thumb?.url_list?.[0] || ''
          },
          video: {
            no_watermark: video.video?.playAddr || video.video?.play_addr?.url_list?.[0] || '',
            with_watermark: video.video?.downloadAddr || video.video?.download_addr?.url_list?.[0] || '',
            cover: video.video?.cover || video.video?.cover?.url_list?.[0] || '',
            duration: video.video?.duration || 0
          },
          images: video.imagePost?.images?.map(img => img.imageURL?.urlList?.[0]) ||
                   video.image_post_info?.images?.map(img => img.display_image?.url_list?.[0]) || [],
          stats: {
            digg_count: video.stats?.diggCount || video.statistics?.digg_count || 0,
            comment_count: video.stats?.commentCount || video.statistics?.comment_count || 0,
            share_count: video.stats?.shareCount || video.statistics?.share_count || 0,
            play_count: video.stats?.playCount || video.statistics?.play_count || 0
          }
        };
      }
    }

    return null;
  } catch (error) {
    console.log('⚠️ JSON数据提取失败:', error.message);
    return null;
  }
}

/**
 * 第三方API解析方法 (简化版)
 */
async function parseWithThirdPartyAPI(url) {
  console.log('🔌 尝试第三方API解析...');

  // 简化的第三方API列表
  const apis = [
    {
      name: 'TikWM API',
      url: 'https://www.tikwm.com/api/',
      getData: () => JSON.stringify({ url: url, hd: 1 }),
      headers: { 'Content-Type': 'application/json' }
    }
  ];

  for (const api of apis) {
    try {
      console.log(`🔄 尝试 ${api.name}...`);

      const response = await makeRequest(api.url, {
        method: 'POST',
        headers: {
          ...CONFIG.headers,
          ...api.headers
        },
        body: api.getData()
      });

      if (response.statusCode !== 200) {
        throw new Error(`HTTP ${response.statusCode}`);
      }

      let data;
      try {
        data = JSON.parse(response.body);
      } catch (e) {
        throw new Error('响应不是有效的JSON');
      }

      if (data.code === 0 && data.data) {
        console.log(`✅ ${api.name} 解析成功`);
        return {
          method: api.name,
          success: true,
          data: {
            id: data.data.id,
            title: data.data.title,
            author: {
              unique_id: data.data.author?.unique_id,
              nickname: data.data.author?.nickname,
              avatar: data.data.author?.avatar
            },
            video: {
              no_watermark: data.data.play,
              with_watermark: data.data.wmplay,
              cover: data.data.cover,
              duration: data.data.duration
            },
            stats: {
              digg_count: data.data.digg_count,
              comment_count: data.data.comment_count,
              share_count: data.data.share_count,
              play_count: data.data.play_count
            }
          }
        };
      }

    } catch (error) {
      console.log(`❌ ${api.name} 失败:`, error.message);
    }
  }

  return { method: 'Third Party API', success: false, error: '第三方API解析失败' };
}

/**
 * 主解析函数
 */
async function parseTikTokVideo(url) {
  console.log('🚀 开始解析TikTok视频...');
  console.log('📎 输入URL:', url);

  try {
    // 1. 处理短链接
    const resolvedUrl = await resolveShortUrl(url);

    // 2. 提取视频ID
    const videoId = extractVideoId(resolvedUrl);
    if (!videoId) {
      throw new Error('无法提取视频ID');
    }

    // 3. 尝试API解析
    const apiResult = await parseWithAPI(videoId);
    if (apiResult.success) {
      return apiResult;
    }

    // 4. 尝试第三方API解析
    console.log('🔄 官方API失败，尝试第三方API...');
    const thirdPartyResult = await parseWithThirdPartyAPI(resolvedUrl);
    if (thirdPartyResult.success) {
      return thirdPartyResult;
    }

    // 5. 尝试网页解析
    console.log('🔄 第三方API失败，尝试网页解析...');
    const webResult = await parseWithWebScraping(resolvedUrl);
    if (webResult.success) {
      return webResult;
    }

    // 所有方法都失败了
    return {
      success: false,
      error: '所有解析方法都失败了',
      attempts: [apiResult, thirdPartyResult, webResult]
    };

  } catch (error) {
    console.log('💥 解析过程出错:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 格式化输出结果
 */
function formatResult(result) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 解析结果');
  console.log('='.repeat(60));
  
  if (result.success) {
    console.log('✅ 解析成功');
    console.log('🔧 解析方法:', result.method);
    
    if (result.data) {
      console.log('\n📹 视频信息:');
      if (result.data.id) console.log('  ID:', result.data.id);
      if (result.data.desc) console.log('  描述:', result.data.desc);
      if (result.data.title) console.log('  标题:', result.data.title);
      
      if (result.data.author) {
        console.log('\n👤 作者信息:');
        if (result.data.author.unique_id) console.log('  用户名:', result.data.author.unique_id);
        if (result.data.author.nickname) console.log('  昵称:', result.data.author.nickname);
      }
      
      if (result.data.video) {
        console.log('\n🎬 视频链接:');
        if (result.data.video.no_watermark) {
          console.log('  无水印链接:', result.data.video.no_watermark);
        }
        if (result.data.video.with_watermark) {
          console.log('  有水印链接:', result.data.video.with_watermark);
        }
        if (result.data.video.url) {
          console.log('  视频链接:', result.data.video.url);
        }
        if (result.data.video.cover) {
          console.log('  封面图片:', result.data.video.cover);
        }
      }
      
      if (result.data.images && result.data.images.length > 0) {
        console.log('\n🖼️  图集模式:');
        result.data.images.forEach((img, index) => {
          console.log(`  图片${index + 1}:`, img);
        });
      }
      
      if (result.data.stats) {
        console.log('\n📈 统计数据:');
        console.log('  点赞数:', result.data.stats.digg_count);
        console.log('  评论数:', result.data.stats.comment_count);
        console.log('  分享数:', result.data.stats.share_count);
        console.log('  播放数:', result.data.stats.play_count);
      }
    }
  } else {
    console.log('❌ 解析失败');
    console.log('💥 错误信息:', result.error);
    
    if (result.attempts) {
      console.log('\n🔍 尝试的方法:');
      result.attempts.forEach((attempt, index) => {
        console.log(`  ${index + 1}. ${attempt.method}: ${attempt.success ? '成功' : '失败'}`);
        if (!attempt.success && attempt.error) {
          console.log(`     错误: ${attempt.error}`);
        }
      });
    }
  }
  
  console.log('='.repeat(60));
}

/**
 * 主测试函数
 */
async function runTest() {
  console.log('🧪 TikTok视频解析测试工具');
  console.log('📝 支持解析TikTok视频并获取无水印链接\n');
  
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  const askForUrl = () => {
    rl.question('请输入TikTok视频URL (输入 q 退出): ', async (input) => {
      const url = input.trim();
      
      if (url.toLowerCase() === 'q') {
        console.log('👋 再见！');
        rl.close();
        return;
      }
      
      if (!url) {
        console.log('❌ 请输入有效的URL');
        askForUrl();
        return;
      }
      
      console.log('\n🔄 开始解析...');
      const result = await parseTikTokVideo(url);
      formatResult(result);
      
      console.log('\n');
      askForUrl();
    });
  };
  
  askForUrl();
}

// 如果直接运行此脚本
if (require.main === module) {
  runTest().catch(console.error);
}

// 导出函数供其他模块使用
module.exports = {
  parseTikTokVideo,
  extractVideoId,
  resolveShortUrl
};
