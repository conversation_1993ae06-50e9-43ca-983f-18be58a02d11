/**
 * 简化版TikTok视频解析脚本
 * 基于项目中uniCloud-aliyun/cloudfunctions/parse-video-link/index.js的抖音解析逻辑
 * 仅使用Node.js内置模块，无需安装额外依赖
 *
 * 使用方法:
 * node simple-tiktok-parser.js
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');
const readline = require('readline');

/**
 * 获取TikTok请求头
 * 完全参考项目中getDefaultHeaders('tiktok')的逻辑
 */
function getTikTokHeaders() {
  // TikTok使用移动端 User-Agent，模拟手机浏览器访问
  const mobileUserAgents = [
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36"
  ];
  const randomUserAgent = mobileUserAgents[Math.floor(Math.random() * mobileUserAgents.length)];
  return {
    "User-Agent": randomUserAgent,
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
    "Accept-Encoding": "gzip, deflate, br",
    "Referer": "https://www.tiktok.com/",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin"
  };
}

// 配置
const CONFIG = {
  timeout: 30000, // 增加到30秒
  maxRetries: 2   // 减少重试次数，避免等待太久
};

/**
 * 发起HTTP/HTTPS请求 (支持POST)
 */
function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;

    const method = options.method || 'GET';
    const body = options.body || null;

    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: method,
      headers: {
        ...getTikTokHeaders(),
        ...options.headers
      },
      timeout: CONFIG.timeout
    };

    // 如果有请求体，设置Content-Length
    if (body) {
      requestOptions.headers['Content-Length'] = Buffer.byteLength(body);
    }

    const req = client.request(requestOptions, (res) => {
      let data = '';

      // 处理重定向
      if (res.statusCode >= 300 && res.statusCode < 400 && res.headers.location) {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: ''
        });
        return;
      }

      res.setEncoding('utf8');

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        resolve({
          statusCode: res.statusCode,
          headers: res.headers,
          body: data
        });
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    // 写入请求体
    if (body) {
      req.write(body);
    }

    req.end();
  });
}

/**
 * 提取TikTok视频ID
 */
function extractVideoId(url) {
  console.log('🔍 提取视频ID:', url);
  
  const patterns = [
    /\/video\/(\d+)/,
    /\/photo\/(\d+)/,
    /\/t\/([A-Za-z0-9]+)/,
    /vm\.tiktok\.com\/([A-Za-z0-9]+)/,
    /vt\.tiktok\.com\/([A-Za-z0-9]+)/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      let videoId = match[1];
      // 如果ID太长，可能包含查询参数，需要截取
      if (videoId.length > 19) {
        const questionIndex = videoId.indexOf('?');
        if (questionIndex > 0) {
          videoId = videoId.substring(0, questionIndex);
        }
      }
      console.log('✅ 找到视频ID:', videoId);
      return videoId;
    }
  }
  
  console.log('❌ 无法提取视频ID');
  return null;
}

/**
 * 处理短链接重定向
 */
async function resolveShortUrl(url) {
  if (!url.includes('vm.tiktok.com') && !url.includes('vt.tiktok.com')) {
    return url;
  }
  
  console.log('🔄 处理短链接重定向...');
  
  try {
    const response = await makeRequest(url, { method: 'HEAD' });
    
    if (response.statusCode >= 300 && response.statusCode < 400) {
      const location = response.headers.location;
      if (location) {
        console.log('✅ 重定向到:', location);
        return location;
      }
    }
  } catch (error) {
    console.log('⚠️ 重定向失败，使用原URL:', error.message);
  }
  
  return url;
}

/**
 * 桌面端网页解析方法
 */
async function parseWithDesktopWeb(url) {
  console.log('🖥️ 尝试桌面端网页解析...');

  try {
    const response = await makeRequest(url, {
      headers: {
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    if (response.statusCode !== 200) {
      throw new Error(`HTTP ${response.statusCode}`);
    }

    const html = response.body;

    // 提取页面中的JSON数据
    const jsonPatterns = [
      // 主要数据源
      /__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*({.+?})\s*<\/script>/s,
      // 备用数据源
      /window\.__INITIAL_STATE__\s*=\s*({.+?});/s,
      /SIGI_STATE\s*=\s*({.+?});/s,
      /"ItemModule":\s*({.+?})/s
    ];

    for (const pattern of jsonPatterns) {
      const match = html.match(pattern);
      if (match) {
        try {
          const jsonData = JSON.parse(match[1]);
          console.log('✅ 找到桌面端JSON数据');

          const videoData = extractVideoFromDesktopJson(jsonData);
          if (videoData) {
            console.log('✅ 桌面端解析成功');
            return {
              method: 'Desktop Web Parsing',
              success: true,
              data: videoData
            };
          }
        } catch (e) {
          console.log('⚠️ JSON解析失败，尝试下一个模式');
          continue;
        }
      }
    }

    // 降级方案：从meta标签提取基本信息
    const basicInfo = extractBasicInfoFromHtml(html);
    if (basicInfo) {
      console.log('✅ 桌面端基本信息解析成功');
      return {
        method: 'Desktop Web Basic',
        success: true,
        data: basicInfo
      };
    }

    throw new Error('无法从桌面端网页提取视频信息');

  } catch (error) {
    console.log('❌ 桌面端解析失败:', error.message);
    return { method: 'Desktop Web Parsing', success: false, error: error.message };
  }
}

/**
 * 从桌面端JSON数据中提取视频信息
 */
function extractVideoFromDesktopJson(jsonData) {
  try {
    // 尝试不同的数据路径
    const paths = [
      'default.webapp.video-detail.itemInfo.itemStruct',
      'default.ItemModule',
      'ItemModule'
    ];

    for (const path of paths) {
      let current = jsonData;
      const parts = path.split('.');

      for (const part of parts) {
        if (current && current[part]) {
          current = current[part];
        } else {
          current = null;
          break;
        }
      }

      if (current && current.itemInfo) {
        const video = current.itemInfo.itemStruct;

        return {
          id: video.id,
          desc: video.desc || '',
          author: {
            unique_id: video.author?.uniqueId || '',
            nickname: video.author?.nickname || '',
            avatar: video.author?.avatarThumb || ''
          },
          video: {
            // 无水印链接
            no_watermark: video.video?.playAddr || '',
            // 有水印链接
            with_watermark: video.video?.downloadAddr || '',
            cover: video.video?.cover || '',
            duration: video.video?.duration || 0
          },
          // 图集模式
          images: video.imagePost?.images?.map(img => img.imageURL?.urlList?.[0]) || [],
          stats: {
            digg_count: video.stats?.diggCount || 0,
            comment_count: video.stats?.commentCount || 0,
            share_count: video.stats?.shareCount || 0,
            play_count: video.stats?.playCount || 0
          }
        };
      }
    }

    return null;
  } catch (error) {
    console.log('⚠️ 桌面端JSON数据提取失败:', error.message);
    return null;
  }
}

/**
 * 网页解析方法 (增强版)
 */
async function parseWithWebScraping(url) {
  console.log('🌐 尝试网页解析...');

  try {
    const response = await makeRequest(url);

    if (response.statusCode !== 200) {
      throw new Error(`HTTP ${response.statusCode}`);
    }

    const html = response.body;

    // 简单的正则提取（不使用cheerio）
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
    const title = titleMatch ? titleMatch[1].replace(/\s*\|\s*TikTok$/, '').trim() : '';

    const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)/i);
    const description = descMatch ? descMatch[1] : '';

    const videoUrlMatch = html.match(/<meta[^>]*property=["']og:video["'][^>]*content=["']([^"']*)/i);
    const videoUrl = videoUrlMatch ? videoUrlMatch[1] : '';

    const imageMatch = html.match(/<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']*)/i);
    const image = imageMatch ? imageMatch[1] : '';

    // 尝试提取JSON数据 - 多种模式
    const jsonPatterns = [
      /__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*({.+?})\s*<\/script>/s,
      /window\.__INITIAL_STATE__\s*=\s*({.+?});/s,
      /SIGI_STATE\s*=\s*({.+?});/s,
      /"ItemModule":\s*({.+?})/s
    ];

    let jsonData = null;
    let extractedVideoData = null;

    for (const pattern of jsonPatterns) {
      const jsonMatch = html.match(pattern);
      if (jsonMatch) {
        try {
          jsonData = JSON.parse(jsonMatch[1]);
          console.log('✅ 找到JSON数据');

          // 尝试从JSON中提取视频信息
          extractedVideoData = extractVideoFromJson(jsonData);
          if (extractedVideoData) {
            console.log('✅ 从JSON中提取到视频数据');
            break;
          }
        } catch (e) {
          console.log('⚠️ JSON解析失败，尝试下一个模式');
          continue;
        }
      }
    }

    // 如果从JSON中提取到了完整数据，返回详细信息
    if (extractedVideoData) {
      return {
        method: 'Web Scraping (Enhanced)',
        success: true,
        data: extractedVideoData
      };
    }

    // 否则返回基本信息
    if (title || videoUrl || jsonData) {
      console.log('✅ 网页解析成功（基本模式）');
      return {
        method: 'Web Scraping',
        success: true,
        data: {
          title: title,
          description: description,
          video: {
            url: videoUrl,
            cover: image
          },
          jsonData: jsonData ? '已提取' : '未找到'
        }
      };
    }

    throw new Error('无法从网页中提取有效信息');

  } catch (error) {
    console.log('❌ 网页解析失败:', error.message);
    return { method: 'Web Scraping', success: false, error: error.message };
  }
}

/**
 * 从JSON数据中提取视频信息
 */
function extractVideoFromJson(jsonData) {
  try {
    // 尝试不同的JSON结构路径
    const paths = [
      'default.webapp.video-detail.itemInfo.itemStruct',
      'default.ItemModule',
      'ItemModule',
      'webapp.video-detail.itemInfo.itemStruct'
    ];

    for (const path of paths) {
      let current = jsonData;
      const parts = path.split('.');

      for (const part of parts) {
        if (current && current[part]) {
          current = current[part];
        } else {
          current = null;
          break;
        }
      }

      if (current && (current.id || current.aweme_id)) {
        const video = current;

        return {
          id: video.id || video.aweme_id,
          desc: video.desc || video.description || '',
          author: {
            unique_id: video.author?.uniqueId || video.author?.unique_id || '',
            nickname: video.author?.nickname || '',
            avatar: video.author?.avatarThumb || video.author?.avatar_thumb?.url_list?.[0] || ''
          },
          video: {
            no_watermark: video.video?.playAddr || video.video?.play_addr?.url_list?.[0] || '',
            with_watermark: video.video?.downloadAddr || video.video?.download_addr?.url_list?.[0] || '',
            cover: video.video?.cover || video.video?.cover?.url_list?.[0] || '',
            duration: video.video?.duration || 0
          },
          images: video.imagePost?.images?.map(img => img.imageURL?.urlList?.[0]) ||
                   video.image_post_info?.images?.map(img => img.display_image?.url_list?.[0]) || [],
          stats: {
            digg_count: video.stats?.diggCount || video.statistics?.digg_count || 0,
            comment_count: video.stats?.commentCount || video.statistics?.comment_count || 0,
            share_count: video.stats?.shareCount || video.statistics?.share_count || 0,
            play_count: video.stats?.playCount || video.statistics?.play_count || 0
          }
        };
      }
    }

    return null;
  } catch (error) {
    console.log('⚠️ JSON数据提取失败:', error.message);
    return null;
  }
}

/**
 * 第三方API解析方法 (简化版)
 */
async function parseWithThirdPartyAPI(url) {
  console.log('🔌 尝试第三方API解析...');

  // 简化的第三方API列表
  const apis = [
    {
      name: 'TikWM API',
      url: 'https://www.tikwm.com/api/',
      getData: () => JSON.stringify({ url: url, hd: 1 }),
      headers: { 'Content-Type': 'application/json' }
    }
  ];

  for (const api of apis) {
    try {
      console.log(`🔄 尝试 ${api.name}...`);

      const response = await makeRequest(api.url, {
        method: 'POST',
        headers: {
          ...CONFIG.headers,
          ...api.headers
        },
        body: api.getData()
      });

      if (response.statusCode !== 200) {
        throw new Error(`HTTP ${response.statusCode}`);
      }

      let data;
      try {
        data = JSON.parse(response.body);
      } catch (e) {
        throw new Error('响应不是有效的JSON');
      }

      if (data.code === 0 && data.data) {
        console.log(`✅ ${api.name} 解析成功`);
        return {
          method: api.name,
          success: true,
          data: {
            id: data.data.id,
            title: data.data.title,
            author: {
              unique_id: data.data.author?.unique_id,
              nickname: data.data.author?.nickname,
              avatar: data.data.author?.avatar
            },
            video: {
              no_watermark: data.data.play,
              with_watermark: data.data.wmplay,
              cover: data.data.cover,
              duration: data.data.duration
            },
            stats: {
              digg_count: data.data.digg_count,
              comment_count: data.data.comment_count,
              share_count: data.data.share_count,
              play_count: data.data.play_count
            }
          }
        };
      }

    } catch (error) {
      console.log(`❌ ${api.name} 失败:`, error.message);
    }
  }

  return { method: 'Third Party API', success: false, error: '第三方API解析失败' };
}

/**
 * 从分享文本中提取TikTok链接
 * 参考项目中extractTiktokUrl函数的逻辑
 */
function extractTikTokUrl(shareText) {
  console.log("开始提取TikTok链接:", shareText);

  // 支持多种TikTok链接格式 - 简化匹配规则，只要包含tiktok域名即可
  const patterns = [
    // 通用TikTok链接匹配 - 匹配所有包含tiktok.com的链接（包括有无路径的情况）
    /https?:\/\/[^.\s]*\.?tiktok\.com[^\s]*/g,
    // 兼容没有子域名的情况
    /https?:\/\/tiktok\.com[^\s]*/g
  ];

  for (const pattern of patterns) {
    const matches = shareText.match(pattern);
    if (matches && matches.length > 0) {
      const url = matches[0];
      console.log("成功提取TikTok链接:", url);
      return url;
    }
  }

  console.log("未找到有效的TikTok链接");
  return null;
}

/**
 * TikTok请求重试机制
 * 参考项目中retryTiktokRequest函数的逻辑
 */
async function retryTikTokRequest(requestFn, maxRetries = 3, baseDelay = 1000) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`TikTok请求尝试 ${attempt}/${maxRetries}`);
      return await requestFn();
    } catch (error) {
      lastError = error;
      console.warn(`TikTok请求第${attempt}次失败:`, error.message);

      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        break;
      }

      // 指数退避延迟
      const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
      console.log(`等待 ${delay}ms 后重试...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * 解析TikTok分享URL获取视频信息
 * 完全参考项目中parseTiktokShareUrl函数的逻辑
 */
async function parseTikTokShareUrl(shareUrl) {
  console.log("开始解析TikTok分享URL:", shareUrl);

  let requestUrl = shareUrl;

  // 处理短链接，需要先获取重定向后的完整URL
  if (shareUrl.includes("vm.tiktok.com") || shareUrl.includes("vt.tiktok.com")) {
    try {
      const redirectResponse = await retryTikTokRequest(async () => {
        return await makeRequest(shareUrl, {
          method: "GET",
          headers: getTikTokHeaders()
        });
      }, 2, 500);

      if (redirectResponse.statusCode >= 300 && redirectResponse.statusCode < 400) {
        const location = redirectResponse.headers.location;
        if (location) {
          requestUrl = location;
          console.log("短链接重定向到:", requestUrl);
        }
      }
    } catch (error) {
      console.warn("获取TikTok重定向URL失败:", error.message);
    }
  }

  // 获取TikTok页面内容，使用重试机制
  const response = await retryTikTokRequest(async () => {
    console.log("发起HTTP请求到:", requestUrl);
    const startTime = Date.now();

    const resp = await makeRequest(requestUrl, {
      method: "GET",
      headers: getTikTokHeaders()
    });

    const requestTime = Date.now() - startTime;
    console.log("HTTP请求完成:", {
      status: resp.statusCode,
      requestTime: `${requestTime}ms`,
      contentLength: resp.body ? resp.body.length : 0
    });

    if (resp.statusCode !== 200) {
      throw new Error(`HTTP请求失败，状态码: ${resp.statusCode}`);
    }

    return resp;
  }, 3, 1500);

  const htmlContent = response.body;
  console.log("HTML内容分析:", {
    length: htmlContent.length
  });

  // 检查是否获取到了TikTok的页面
  if (!htmlContent.includes('tiktok') && !htmlContent.includes('TikTok')) {
    console.warn("页面内容可能不是TikTok页面");
    console.log("页面内容预览:", htmlContent.substring(0, 500));
    throw new Error("未获取到TikTok页面内容，可能是链接已失效或被限制访问");
  }

  console.log("✅ 确认获取到TikTok页面内容");

  // 解析页面中的JSON数据
  return parseTikTokPageData(htmlContent);
}

/**
 * 解析TikTok页面数据
 * 参考项目中的JSON数据解析逻辑
 */
function parseTikTokPageData(htmlContent) {
  console.log("开始解析TikTok页面数据...");

  let jsonData = null;

  // 方式1: 尝试解析 __UNIVERSAL_DATA_FOR_REHYDRATION__
  const universalDataPattern = /__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*(.*?)<\/script>/s;
  let match = universalDataPattern.exec(htmlContent);

  if (match && match[1]) {
    console.log("找到 __UNIVERSAL_DATA_FOR_REHYDRATION__ 数据块");
    try {
      const jsonStr = match[1].trim();
      jsonData = JSON.parse(jsonStr);
      console.log("✅ 成功解析 __UNIVERSAL_DATA_FOR_REHYDRATION__ 数据");
    } catch (error) {
      console.warn("❌ 解析 __UNIVERSAL_DATA_FOR_REHYDRATION__ 失败:", error.message);
    }
  }

  // 方式2: 尝试解析 window.__INITIAL_SSR_STATE__
  if (!jsonData) {
    const ssrStatePattern = /window\.__INITIAL_SSR_STATE__\s*=\s*(.*?)<\/script>/s;
    match = ssrStatePattern.exec(htmlContent);

    if (match && match[1]) {
      console.log("找到 __INITIAL_SSR_STATE__ 数据块");
      try {
        let jsonStr = match[1].trim();
        jsonStr = jsonStr.replace(/:\s*undefined/g, ': null');
        jsonData = JSON.parse(jsonStr);
        console.log("✅ 成功解析 __INITIAL_SSR_STATE__ 数据");
      } catch (error) {
        console.warn("❌ 解析 __INITIAL_SSR_STATE__ 失败:", error.message);
      }
    }
  }

  // 方式3: 降级方案
  if (!jsonData) {
    console.log("使用降级方案提取基本信息");
    return extractBasicInfoFromMeta(htmlContent);
  }

  // 从JSON数据中提取视频信息
  return extractVideoInfoFromJson(jsonData);
}

/**
 * 从HTML meta标签中提取基本信息
 */
function extractBasicInfoFromMeta(htmlContent) {
  const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
  const title = titleMatch ? titleMatch[1].replace(/\s*\|\s*TikTok$/, '').trim() : "";

  const videoUrlMatch = htmlContent.match(/<meta[^>]*property=["']og:video["'][^>]*content=["']([^"']*)/i);
  const videoUrl = videoUrlMatch ? videoUrlMatch[1] : "";

  const coverMatch = htmlContent.match(/<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']*)/i);
  const coverUrl = coverMatch ? coverMatch[1] : "";

  if (title || videoUrl) {
    return {
      video_url: videoUrl,
      cover_url: coverUrl,
      title: title,
      images: [],
      author: {
        uid: "",
        name: "TikTok用户",
        avatar: "",
      }
    };
  }

  throw new Error("无法从TikTok页面中提取视频信息");
}

/**
 * 从JSON数据中提取视频信息
 */
function extractVideoInfoFromJson(jsonData) {
  console.log("从JSON数据中提取视频信息...");

  let videoInfo = null;

  // 尝试不同的数据路径
  const possiblePaths = [
    'default.webapp.video-detail',
    'default.ItemModule',
    'ItemModule',
    'webapp.video-detail'
  ];

  for (const path of possiblePaths) {
    const pathParts = path.split('.');
    let current = jsonData;

    for (const part of pathParts) {
      if (current && current[part]) {
        current = current[part];
      } else {
        current = null;
        break;
      }
    }

    if (current && current.itemInfo) {
      videoInfo = current.itemInfo.itemStruct;
      console.log("找到视频信息，路径:", path);
      break;
    }
  }

  if (!videoInfo) {
    throw new Error("无法在JSON数据中找到视频信息");
  }

  // 提取视频播放地址
  let videoUrl = "";
  if (videoInfo.video && videoInfo.video.playAddr) {
    videoUrl = videoInfo.video.playAddr;
  } else if (videoInfo.video && videoInfo.video.downloadAddr) {
    videoUrl = videoInfo.video.downloadAddr;
  }

  // 提取封面图
  let coverUrl = "";
  if (videoInfo.video && videoInfo.video.cover) {
    coverUrl = videoInfo.video.cover;
  } else if (videoInfo.video && videoInfo.video.originCover) {
    coverUrl = videoInfo.video.originCover;
  }

  return {
    video_url: videoUrl,
    cover_url: coverUrl,
    title: videoInfo.desc || "",
    images: [],
    author: {
      uid: videoInfo.author?.id || videoInfo.author?.uniqueId || "",
      name: videoInfo.author?.nickname || videoInfo.author?.uniqueId || "TikTok用户",
      avatar: videoInfo.author?.avatarThumb || videoInfo.author?.avatarMedium || "",
    }
  };
}

/**
 * 主解析函数
 * 完全基于项目中的抖音解析逻辑
 */
async function parseTikTokVideo(inputText) {
  console.log('🚀 开始解析TikTok视频...');
  console.log('📎 输入内容:', inputText);
  console.log('🔍 解析策略: 基于项目抖音逻辑');

  try {
    // 1. 提取TikTok链接
    const tiktokUrl = extractTikTokUrl(inputText);
    if (!tiktokUrl) {
      throw new Error("未找到有效的TikTok链接");
    }
    console.log('🔗 提取到的TikTok链接:', tiktokUrl);

    // 2. 解析TikTok视频信息
    const result = await parseTikTokShareUrl(tiktokUrl);

    console.log('🎉 TikTok解析成功！');
    return {
      success: true,
      method: 'TikTok Web Parsing (Based on Douyin Logic)',
      data: result
    };

  } catch (error) {
    console.log('💥 TikTok解析失败:', error.message);

    let errorMessage = "TikTok视频解析失败";

    if (error.message.includes("未找到有效的TikTok链接")) {
      errorMessage = "未找到有效的TikTok链接，请检查输入内容";
    } else if (error.message.includes("HTTP请求失败")) {
      errorMessage = "网络请求失败，请稍后重试";
    } else if (error.message.includes("未获取到TikTok页面内容")) {
      errorMessage = "无法访问TikTok页面，可能是链接已失效或地区限制";
    }

    return {
      success: false,
      error: errorMessage,
      originalError: error.message
    };
  }
}

/**
 * 格式化输出结果
 */
function formatResult(result) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 解析结果');
  console.log('='.repeat(60));
  
  if (result.success) {
    console.log('✅ 解析成功');
    console.log('🔧 解析方法:', result.method);
    
    if (result.data) {
      console.log('\n📹 视频信息:');
      if (result.data.id) console.log('  ID:', result.data.id);
      if (result.data.desc) console.log('  描述:', result.data.desc);
      if (result.data.title) console.log('  标题:', result.data.title);
      
      if (result.data.author) {
        console.log('\n👤 作者信息:');
        if (result.data.author.unique_id) console.log('  用户名:', result.data.author.unique_id);
        if (result.data.author.nickname) console.log('  昵称:', result.data.author.nickname);
      }
      
      if (result.data.video) {
        console.log('\n🎬 视频链接:');
        if (result.data.video.no_watermark) {
          console.log('  无水印链接:', result.data.video.no_watermark);
        }
        if (result.data.video.with_watermark) {
          console.log('  有水印链接:', result.data.video.with_watermark);
        }
        if (result.data.video.url) {
          console.log('  视频链接:', result.data.video.url);
        }
        if (result.data.video.cover) {
          console.log('  封面图片:', result.data.video.cover);
        }
      }
      
      if (result.data.images && result.data.images.length > 0) {
        console.log('\n🖼️  图集模式:');
        result.data.images.forEach((img, index) => {
          console.log(`  图片${index + 1}:`, img);
        });
      }
      
      if (result.data.stats) {
        console.log('\n📈 统计数据:');
        console.log('  点赞数:', result.data.stats.digg_count);
        console.log('  评论数:', result.data.stats.comment_count);
        console.log('  分享数:', result.data.stats.share_count);
        console.log('  播放数:', result.data.stats.play_count);
      }
    }
  } else {
    console.log('❌ 解析失败');
    console.log('💥 错误信息:', result.error);
    
    if (result.attempts) {
      console.log('\n🔍 尝试的方法:');
      result.attempts.forEach((attempt, index) => {
        console.log(`  ${index + 1}. ${attempt.method}: ${attempt.success ? '成功' : '失败'}`);
        if (!attempt.success && attempt.error) {
          console.log(`     错误: ${attempt.error}`);
        }
      });
    }
  }
  
  console.log('='.repeat(60));
}

/**
 * 格式化输出结果
 */
function formatResult(result) {
  console.log('\n' + '='.repeat(60));
  console.log('📊 TikTok解析结果');
  console.log('='.repeat(60));

  if (result.success) {
    console.log('✅ 解析成功');
    console.log('🔧 解析方法:', result.method);

    if (result.data) {
      const data = result.data;
      console.log('\n📹 视频信息:');
      if (data.title) console.log('  标题:', data.title);

      if (data.author) {
        console.log('\n👤 作者信息:');
        if (data.author.name) console.log('  昵称:', data.author.name);
        if (data.author.uid) console.log('  用户ID:', data.author.uid);
      }

      if (data.video_url) {
        console.log('\n🎬 视频链接:');
        console.log('  视频URL:', data.video_url);
      }

      if (data.cover_url) {
        console.log('  封面图片:', data.cover_url);
      }
    }
  } else {
    console.log('❌ 解析失败');
    console.log('💥 错误信息:', result.error);
    if (result.originalError) {
      console.log('🔍 详细错误:', result.originalError);
    }
  }

  console.log('='.repeat(60));
}

/**
 * 主测试函数
 */
async function runTest() {
  console.log('🧪 TikTok视频解析测试工具');
  console.log('📝 基于项目中抖音解析逻辑重新设计\n');

  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  const askForInput = () => {
    rl.question('请输入TikTok链接或包含TikTok链接的文本 (输入 q 退出): ', async (input) => {
      const inputText = input.trim();

      if (inputText.toLowerCase() === 'q') {
        console.log('👋 再见！');
        rl.close();
        return;
      }

      if (!inputText) {
        console.log('❌ 请输入有效的内容');
        askForInput();
        return;
      }

      console.log('\n🔄 开始解析...');
      const result = await parseTikTokVideo(inputText);
      formatResult(result);

      console.log('\n');
      askForInput();
    });
  };

  askForInput();
}

// 如果直接运行此脚本
if (require.main === module) {
  runTest().catch(console.error);
}

// 导出函数供其他模块使用
module.exports = {
  parseTikTokVideo,
  extractVideoId,
  resolveShortUrl
};
