/**
 * TikTok视频解析脚本
 * 基于项目中uniCloud-aliyun/cloudfunctions/parse-video-link/index.js的抖音解析逻辑
 * 完全参考抖音的解析模式：网页访问 → JSON提取 → 数据解析
 *
 * 使用方法:
 * node test-tiktok-parser.js
 *
 * 依赖安装:
 * npm install node-fetch
 */

// 检查是否有 node-fetch，如果没有则使用内置的 http/https 模块
const fetch = require("node-fetch");


/**
 * 获取TikTok请求头
 * 完全参考项目中getDefaultHeaders('tiktok')的逻辑
 */
function getTikTokHeaders() {
  // TikTok使用移动端 User-Agent，模拟手机浏览器访问
  const mobileUserAgents = [
    "Mozilla/5.0 (iPhone; CPU iPhone OS 17_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.0 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1",
    "Mozilla/5.0 (Linux; Android 13; SM-G991B) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Mobile Safari/537.36",
    "Mozilla/5.0 (Linux; Android 12; Pixel 6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36",
  ];
  const randomUserAgent = mobileUserAgents[Math.floor(Math.random() * mobileUserAgents.length)];
  return {
    "User-Agent": randomUserAgent,
    Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7",
    "Accept-Encoding": "gzip, deflate, br",
    Referer: "https://www.tiktok.com/",
    "Sec-Fetch-Dest": "document",
    "Sec-Fetch-Mode": "navigate",
    "Sec-Fetch-Site": "same-origin",
  };
}

/**
 * 延迟函数
 */
const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

/**
 * TikTok请求重试机制
 * 参考项目中的抖音重试逻辑
 */
async function retryTikTokRequest(requestFn, maxRetries = 3, baseDelay = 1000) {
  let lastError;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`TikTok请求尝试 ${attempt}/${maxRetries}`);
      return await requestFn();
    } catch (error) {
      lastError = error;
      console.warn(`TikTok请求第${attempt}次失败:`, error.message);

      // 如果是最后一次尝试，直接抛出错误
      if (attempt === maxRetries) {
        break;
      }

      // 指数退避延迟
      const delay = baseDelay * Math.pow(2, attempt - 1) + Math.random() * 1000;
      console.log(`等待 ${delay}ms 后重试...`);
      await new Promise((resolve) => setTimeout(resolve, delay));
    }
  }

  throw lastError;
}

/**
 * 从分享文本中提取TikTok链接
 * 参考项目中的抖音链接提取逻辑
 */
function extractTikTokUrl(shareText) {
  console.log("开始提取TikTok链接:", shareText);

  // 支持多种TikTok链接格式
  const patterns = [
    // 标准TikTok链接
    /https?:\/\/www\.tiktok\.com\/@[^\/]+\/video\/\d+/g,
    // 短链接
    /https?:\/\/vm\.tiktok\.com\/[A-Za-z0-9]+\/?/g,
    /https?:\/\/vt\.tiktok\.com\/[A-Za-z0-9]+\/?/g,
    // 移动端链接
    /https?:\/\/m\.tiktok\.com\/v\/\d+/g,
    // 通用TikTok链接匹配
    /https?:\/\/[^.\s]*\.?tiktok\.com[^\s]*/g,
    // 兼容没有子域名的情况
    /https?:\/\/tiktok\.com[^\s]*/g,
  ];

  for (const pattern of patterns) {
    const matches = shareText.match(pattern);
    if (matches && matches.length > 0) {
      const url = matches[0];
      console.log("成功提取TikTok链接:", url);
      return url;
    }
  }

  console.log("未找到有效的TikTok链接");
  return null;
}

/**
 * 解析TikTok分享URL获取视频信息
 * 完全参考项目中parseTiktokShareUrl函数的逻辑
 */
async function parseTikTokShareUrl(shareUrl) {
  console.log("开始解析TikTok分享URL:", shareUrl);

  let requestUrl = shareUrl;

  // 处理短链接，需要先获取重定向后的完整URL
  // 参考项目中的重定向处理逻辑
  if (shareUrl.includes("vm.tiktok.com") || shareUrl.includes("vt.tiktok.com")) {
    try {
      const redirectResponse = await retryTikTokRequest(
        async () => {
          return await fetch(shareUrl, {
            method: "GET",
            headers: getTikTokHeaders(),
            timeout: 15000,
          });
        },
        2,
        500
      ); // 重定向请求使用较少的重试次数

      // 检查是否有重定向
      let location = null;
      if (redirectResponse.headers) {
        if (redirectResponse.headers.get) {
          location = redirectResponse.headers.get("location");
        } else if (redirectResponse.headers.location) {
          location = redirectResponse.headers.location;
        }
      }
      if (location) {
        requestUrl = location;
        console.log("短链接重定向到:", requestUrl);
      }
    } catch (error) {
      console.warn("获取TikTok重定向URL失败:", error.message);
      // 如果重定向失败，继续使用原URL尝试
    }
  }

  // 获取TikTok页面内容，使用重试机制
  // 完全参考项目中的请求逻辑
  console.log("开始请求TikTok页面:", {
    url: requestUrl,
    timeout: 20000,
  });

  const response = await retryTikTokRequest(
    async () => {
      console.log("发起HTTP请求到:", requestUrl);
      const startTime = Date.now();

      const resp = await fetch(requestUrl, {
        method: "GET",
        headers: getTikTokHeaders(),
        timeout: 20000,
      });

      const requestTime = Date.now() - startTime;
      console.log("HTTP请求完成:", {
        status: resp.status,
        statusText: resp.statusText || "OK",
        requestTime: `${requestTime}ms`,
        contentLength:
          resp.headers && resp.headers.get
            ? resp.headers.get("content-length") || "unknown"
            : "unknown",
      });

      if (!resp.ok) {
        console.error("HTTP请求失败详情:", {
          status: resp.status,
          statusText: resp.statusText,
          url: requestUrl,
        });
        throw new Error(`HTTP请求失败，状态码: ${resp.status}`);
      }

      return resp;
    },
    3,
    1500
  ); // 主要请求使用更多重试次数和更长延迟

  const htmlContent = await response.text();
  console.log("HTML内容分析:", {
    length: htmlContent.length,
    contentType:
      response.headers && response.headers.get
        ? response.headers.get("content-type") || "unknown"
        : "unknown",
  });

  // 检查页面内容特征
  const pageFeatures = {
    hasTikTokKeyword: htmlContent.includes("tiktok") || htmlContent.includes("TikTok"),
    hasTitle: htmlContent.includes("<title"),
    hasScript: htmlContent.includes("<script"),
    hasUniversalData: htmlContent.includes("__UNIVERSAL_DATA_FOR_REHYDRATION__"),
    hasSSRState: htmlContent.includes("__INITIAL_SSR_STATE__"),
    hasMetaTags: htmlContent.includes("<meta"),
  };

  console.log("页面特征检查:", pageFeatures);

  // 检查是否获取到了TikTok的页面
  if (!pageFeatures.hasTikTokKeyword) {
    console.warn("页面内容可能不是TikTok页面");
    console.log("页面内容预览:", htmlContent.substring(0, 500));
    const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
    console.log("页面标题:", titleMatch?.[1] || "未找到标题");
    throw new Error("未获取到TikTok页面内容，可能是链接已失效或被限制访问");
  }

  console.log("✅ 确认获取到TikTok页面内容");

  // TikTok的数据可能在多个地方，尝试不同的解析方式
  // 参考项目中的JSON数据提取逻辑
  let jsonData = null;
  console.log("开始尝试解析页面中的JSON数据...");

  // 方式1: 尝试解析 __UNIVERSAL_DATA_FOR_REHYDRATION__
  console.log("方式1: 尝试解析 __UNIVERSAL_DATA_FOR_REHYDRATION__");
  const universalDataPattern = /__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*(.*?)<\/script>/s;
  let match = universalDataPattern.exec(htmlContent);

  if (match && match[1]) {
    console.log("找到 __UNIVERSAL_DATA_FOR_REHYDRATION__ 数据块，长度:", match[1].length);
    try {
      const jsonStr = match[1].trim();
      console.log("JSON字符串预览:", jsonStr.substring(0, 200) + "...");
      jsonData = JSON.parse(jsonStr);
      console.log("✅ 成功解析 __UNIVERSAL_DATA_FOR_REHYDRATION__ 数据");
      console.log("数据结构概览:", Object.keys(jsonData));
    } catch (error) {
      console.warn("❌ 解析 __UNIVERSAL_DATA_FOR_REHYDRATION__ 失败:", error.message);
      console.log("解析失败的JSON开头:", match[1].substring(0, 100));
    }
  } else {
    console.log("❌ 未找到 __UNIVERSAL_DATA_FOR_REHYDRATION__ 数据块");
  }

  // 方式2: 尝试解析 window.__INITIAL_SSR_STATE__
  if (!jsonData) {
    console.log("方式2: 尝试解析 window.__INITIAL_SSR_STATE__");
    const ssrStatePattern = /window\.__INITIAL_SSR_STATE__\s*=\s*(.*?)<\/script>/s;
    match = ssrStatePattern.exec(htmlContent);

    if (match && match[1]) {
      console.log("找到 __INITIAL_SSR_STATE__ 数据块，长度:", match[1].length);
      try {
        let jsonStr = match[1].trim();
        console.log("原始JSON字符串预览:", jsonStr.substring(0, 200) + "...");

        // 处理可能的JavaScript语法
        const originalLength = jsonStr.length;
        jsonStr = jsonStr.replace(/:\s*undefined/g, ": null");
        if (jsonStr.length !== originalLength) {
          console.log("已处理undefined值");
        }

        console.log("处理后JSON字符串预览:", jsonStr.substring(0, 200) + "...");
        jsonData = JSON.parse(jsonStr);
        console.log("✅ 成功解析 __INITIAL_SSR_STATE__ 数据");
        console.log("数据结构概览:", Object.keys(jsonData));
      } catch (error) {
        console.warn("❌ 解析 __INITIAL_SSR_STATE__ 失败:", error.message);
        console.log("解析失败的JSON开头:", match[1].substring(0, 100));
      }
    } else {
      console.log("❌ 未找到 __INITIAL_SSR_STATE__ 数据块");
    }
  }

  // 方式3: 尝试从HTML中提取基本信息（降级方案）
  if (!jsonData) {
    console.log("方式3: 尝试从HTML meta标签中提取基本信息（降级方案）");
    return extractBasicInfoFromMeta(htmlContent);
  }

  // 解析JSON数据获取视频信息
  return extractVideoInfoFromJson(jsonData);
}

/**
 * 从HTML meta标签中提取基本信息（降级方案）
 * 参考项目中的降级处理逻辑
 */
function extractBasicInfoFromMeta(htmlContent) {
  console.log("方式3: 尝试从HTML meta标签中提取基本信息（降级方案）");

  // 提取标题
  const titleMatch = htmlContent.match(/<title[^>]*>(.*?)<\/title>/i);
  const title = titleMatch ? titleMatch[1].replace(/\s*\|\s*TikTok$/, "").trim() : "";
  console.log("提取到的标题:", title || "未找到");

  // 提取描述（可能包含作者信息）
  const descMatch = htmlContent.match(
    /<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)/i
  );
  const description = descMatch ? descMatch[1] : "";
  console.log("提取到的描述:", description || "未找到");

  // 提取视频URL（从meta标签）
  const videoUrlMatch = htmlContent.match(
    /<meta[^>]*property=["']og:video["'][^>]*content=["']([^"']*)/i
  );
  const videoUrl = videoUrlMatch ? videoUrlMatch[1] : "";
  console.log("提取到的视频URL:", videoUrl || "未找到");

  // 提取封面图
  const coverMatch = htmlContent.match(
    /<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']*)/i
  );
  const coverUrl = coverMatch ? coverMatch[1] : "";
  console.log("提取到的封面URL:", coverUrl || "未找到");

  if (title || videoUrl) {
    console.log("✅ 使用降级方案成功提取到基本信息");
    return {
      video_url: videoUrl,
      cover_url: coverUrl,
      title: title || description,
      images: [],
      author: {
        uid: "",
        name: "TikTok用户",
        avatar: "",
      },
    };
  }

  console.error("❌ 降级方案也无法提取到有效信息");
  throw new Error("无法从TikTok页面中提取视频信息，页面结构可能已变更");
}

/**
 * 从JSON数据中提取视频信息
 * 参考项目中的JSON数据解析逻辑
 */
function extractVideoInfoFromJson(jsonData) {
  console.log("开始从JSON数据中提取视频信息...");

  let videoInfo = null;

  try {
    // TikTok的数据结构可能会变化，需要尝试不同的路径
    // 参考项目中的路径查找逻辑
    const possiblePaths = [
      "default.webapp.video-detail",
      "default.ItemModule",
      "ItemModule",
      "webapp.video-detail",
    ];

    for (const path of possiblePaths) {
      const pathParts = path.split(".");
      let current = jsonData;

      for (const part of pathParts) {
        if (current && current[part]) {
          current = current[part];
        } else {
          current = null;
          break;
        }
      }

      if (current && current.itemInfo) {
        videoInfo = current.itemInfo.itemStruct;
        console.log("找到视频信息，路径:", path);
        break;
      }
    }

    if (!videoInfo) {
      // 尝试直接查找包含视频信息的对象
      const findVideoInfo = (obj, depth = 0) => {
        if (depth > 5) return null; // 限制递归深度

        if (obj && typeof obj === "object") {
          // 查找包含视频信息的对象特征
          if (obj.id && obj.desc && obj.video && obj.author) {
            return obj;
          }

          for (const key in obj) {
            const result = findVideoInfo(obj[key], depth + 1);
            if (result) return result;
          }
        }

        return null;
      };

      videoInfo = findVideoInfo(jsonData);
    }

    if (!videoInfo) {
      throw new Error("无法在JSON数据中找到视频信息");
    }
  } catch (error) {
    console.error("解析TikTok JSON数据失败:", error.message);
    throw new Error("TikTok数据解析失败，页面结构可能已变更");
  }

  // 提取视频播放地址
  let videoUrl = "";
  if (videoInfo.video && videoInfo.video.playAddr) {
    videoUrl = videoInfo.video.playAddr;
  } else if (videoInfo.video && videoInfo.video.downloadAddr) {
    videoUrl = videoInfo.video.downloadAddr;
  }

  // 提取封面图
  let coverUrl = "";
  if (videoInfo.video && videoInfo.video.cover) {
    coverUrl = videoInfo.video.cover;
  } else if (videoInfo.video && videoInfo.video.originCover) {
    coverUrl = videoInfo.video.originCover;
  }

  const tiktokVideoInfo = {
    video_url: videoUrl,
    cover_url: coverUrl,
    title: videoInfo.desc || "",
    images: [], // TikTok主要是视频，很少有图集
    author: {
      uid: videoInfo.author?.id || videoInfo.author?.uniqueId || "",
      name: videoInfo.author?.nickname || videoInfo.author?.uniqueId || "TikTok用户",
      avatar: videoInfo.author?.avatarThumb || videoInfo.author?.avatarMedium || "",
    },
  };

  console.log("TikTok视频解析完成:", {
    title: tiktokVideoInfo.title,
    author: tiktokVideoInfo.author.name,
    hasVideo: !!tiktokVideoInfo.video_url,
  });

  return tiktokVideoInfo;
}

/**
 * 提取TikTok视频ID
 */
function extractVideoId(url) {
  console.log("🔍 提取视频ID:", url);

  // 支持多种TikTok URL格式
  const patterns = [
    /\/video\/(\d+)/,
    /\/photo\/(\d+)/,
    /\/t\/([A-Za-z0-9]+)/,
    /vm\.tiktok\.com\/([A-Za-z0-9]+)/,
    /vt\.tiktok\.com\/([A-Za-z0-9]+)/,
  ];

  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      console.log("✅ 找到视频ID:", match[1]);
      return match[1];
    }
  }

  console.log("❌ 无法提取视频ID");
  return null;
}

/**
 * 处理短链接重定向
 */
async function resolveShortUrl(url) {
  if (!url.includes("vm.tiktok.com") && !url.includes("vt.tiktok.com")) {
    return url;
  }

  console.log("🔄 处理短链接重定向...");

  try {
    const response = await fetch(url, {
      method: "HEAD",
      headers: CONFIG.headers,
      redirect: "manual",
      timeout: CONFIG.timeout,
    });

    const location = response.headers.get("location");
    if (location) {
      console.log("✅ 重定向到:", location);
      return location;
    }
  } catch (error) {
    console.log("⚠️ 重定向失败，使用原URL:", error.message);
  }

  return url;
}

/**
 * 方法1: 桌面端网页解析
 */
async function parseWithDesktopWeb(url) {
  console.log("�️ 方法1: 桌面端网页解析...");

  try {
    const response = await fetch(url, {
      headers: {
        ...CONFIG.headers,
        Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
        "Cache-Control": "no-cache",
        Pragma: "no-cache",
      },
      timeout: CONFIG.timeout,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();

    // 提取页面中的JSON数据
    const jsonPatterns = [
      // 主要数据源
      /__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*({.+?})\s*<\/script>/s,
      // 备用数据源
      /window\.__INITIAL_STATE__\s*=\s*({.+?});/s,
      /SIGI_STATE\s*=\s*({.+?});/s,
      /"ItemModule":\s*({.+?})/s,
    ];

    for (const pattern of jsonPatterns) {
      const match = html.match(pattern);
      if (match) {
        try {
          const jsonData = JSON.parse(match[1]);
          console.log("✅ 找到JSON数据，开始解析...");

          const videoData = extractVideoFromDesktopJson(jsonData);
          if (videoData) {
            console.log("✅ 桌面端解析成功");
            return {
              method: "Desktop Web Parsing",
              success: true,
              data: videoData,
            };
          }
        } catch (e) {
          console.log("⚠️ JSON解析失败，尝试下一个模式");
          continue;
        }
      }
    }

    // 降级方案：从meta标签提取基本信息
    const basicInfo = extractBasicInfoFromHtml(html);
    if (basicInfo) {
      console.log("✅ 桌面端基本信息解析成功");
      return {
        method: "Desktop Web Basic",
        success: true,
        data: basicInfo,
      };
    }

    throw new Error("无法从桌面端网页提取视频信息");
  } catch (error) {
    console.log("❌ 桌面端解析失败:", error.message);
    return { method: "Desktop Web Parsing", success: false, error: error.message };
  }
}

/**
 * 从桌面端JSON数据中提取视频信息
 */
function extractVideoFromDesktopJson(jsonData) {
  try {
    // 尝试不同的数据路径
    const paths = [
      "default.webapp.video-detail.itemInfo.itemStruct",
      "default.ItemModule",
      "ItemModule",
    ];

    for (const path of paths) {
      let current = jsonData;
      const parts = path.split(".");

      for (const part of parts) {
        if (current && current[part]) {
          current = current[part];
        } else {
          current = null;
          break;
        }
      }

      if (current && current.itemInfo) {
        const video = current.itemInfo.itemStruct;

        return {
          id: video.id,
          desc: video.desc || "",
          author: {
            unique_id: video.author?.uniqueId || "",
            nickname: video.author?.nickname || "",
            avatar: video.author?.avatarThumb || "",
          },
          video: {
            // 无水印链接
            no_watermark: video.video?.playAddr || "",
            // 有水印链接
            with_watermark: video.video?.downloadAddr || "",
            cover: video.video?.cover || "",
            duration: video.video?.duration || 0,
          },
          // 图集模式
          images: video.imagePost?.images?.map((img) => img.imageURL?.urlList?.[0]) || [],
          stats: {
            digg_count: video.stats?.diggCount || 0,
            comment_count: video.stats?.commentCount || 0,
            share_count: video.stats?.shareCount || 0,
            play_count: video.stats?.playCount || 0,
          },
        };
      }
    }

    return null;
  } catch (error) {
    console.log("⚠️ 桌面端JSON数据提取失败:", error.message);
    return null;
  }
}

/**
 * 从HTML中提取基本信息
 */
function extractBasicInfoFromHtml(html) {
  try {
    // 提取meta标签信息
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
    const title = titleMatch ? titleMatch[1].replace(/\s*\|\s*TikTok$/, "").trim() : "";

    const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)/i);
    const description = descMatch ? descMatch[1] : "";

    const videoUrlMatch = html.match(
      /<meta[^>]*property=["']og:video["'][^>]*content=["']([^"']*)/i
    );
    const videoUrl = videoUrlMatch ? videoUrlMatch[1] : "";

    const imageMatch = html.match(/<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']*)/i);
    const image = imageMatch ? imageMatch[1] : "";

    if (title || videoUrl) {
      return {
        title: title,
        description: description,
        video: {
          url: videoUrl,
          cover: image,
        },
      };
    }

    return null;
  } catch (error) {
    console.log("⚠️ 基本信息提取失败:", error.message);
    return null;
  }
}

/**
 * 方法2: 移动端网页解析
 */
async function parseWithMobileWeb(url) {
  console.log("📱 方法2: 移动端网页解析...");

  try {
    // 构造移动端URL
    const mobileUrl = url.replace("www.tiktok.com", "m.tiktok.com");

    const response = await fetch(mobileUrl, {
      headers: {
        ...CONFIG.headers,
        "User-Agent":
          "Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1",
        Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
        "Accept-Language": "en-US,en;q=0.5",
      },
      timeout: CONFIG.timeout,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();

    // 移动端特有的JSON数据模式
    const mobileJsonPatterns = [
      /window\.__INITIAL_STATE__\s*=\s*({.+?});/s,
      /__NEXT_DATA__\s*=\s*({.+?})\s*<\/script>/s,
      /SIGI_STATE\s*=\s*({.+?});/s,
    ];

    for (const pattern of mobileJsonPatterns) {
      const match = html.match(pattern);
      if (match) {
        try {
          const jsonData = JSON.parse(match[1]);
          console.log("✅ 找到移动端JSON数据");

          const videoData = extractVideoFromMobileJson(jsonData);
          if (videoData) {
            console.log("✅ 移动端解析成功");
            return {
              method: "Mobile Web Parsing",
              success: true,
              data: videoData,
            };
          }
        } catch (e) {
          console.log("⚠️ 移动端JSON解析失败，尝试下一个模式");
          continue;
        }
      }
    }

    // 移动端降级方案
    const basicInfo = extractBasicInfoFromHtml(html);
    if (basicInfo) {
      console.log("✅ 移动端基本信息解析成功");
      return {
        method: "Mobile Web Basic",
        success: true,
        data: basicInfo,
      };
    }

    throw new Error("无法从移动端网页提取视频信息");
  } catch (error) {
    console.log("❌ 移动端解析失败:", error.message);
    return { method: "Mobile Web Parsing", success: false, error: error.message };
  }
}

/**
 * 从移动端JSON数据中提取视频信息
 */
function extractVideoFromMobileJson(jsonData) {
  try {
    // 移动端数据路径
    const paths = [
      "props.pageProps.itemInfo.itemStruct",
      "props.pageProps.videoData",
      "itemInfo.itemStruct",
      "videoData",
    ];

    for (const path of paths) {
      let current = jsonData;
      const parts = path.split(".");

      for (const part of parts) {
        if (current && current[part]) {
          current = current[part];
        } else {
          current = null;
          break;
        }
      }

      if (current && (current.id || current.aweme_id)) {
        const video = current;

        return {
          id: video.id || video.aweme_id,
          desc: video.desc || video.description || "",
          author: {
            unique_id: video.author?.uniqueId || video.author?.unique_id || "",
            nickname: video.author?.nickname || "",
            avatar: video.author?.avatarThumb || video.author?.avatar_thumb?.url_list?.[0] || "",
          },
          video: {
            no_watermark: video.video?.playAddr || video.video?.play_addr?.url_list?.[0] || "",
            with_watermark:
              video.video?.downloadAddr || video.video?.download_addr?.url_list?.[0] || "",
            cover: video.video?.cover || video.video?.cover?.url_list?.[0] || "",
            duration: video.video?.duration || 0,
          },
          images:
            video.imagePost?.images?.map((img) => img.imageURL?.urlList?.[0]) ||
            video.image_post_info?.images?.map((img) => img.display_image?.url_list?.[0]) ||
            [],
          stats: {
            digg_count: video.stats?.diggCount || video.statistics?.digg_count || 0,
            comment_count: video.stats?.commentCount || video.statistics?.comment_count || 0,
            share_count: video.stats?.shareCount || video.statistics?.share_count || 0,
            play_count: video.stats?.playCount || video.statistics?.play_count || 0,
          },
        };
      }
    }

    return null;
  } catch (error) {
    console.log("⚠️ 移动端JSON数据提取失败:", error.message);
    return null;
  }
}

/**
 * 方法3: 第三方API解析 (2024-2025最新)
 */
async function parseWithThirdPartyAPI(url) {
  console.log("🔌 方法3: 尝试第三方API解析...");

  // 基于最新搜索结果的第三方API服务
  const apis = [
    {
      name: "TikWM API",
      url: "https://www.tikwm.com/api/",
      method: "POST",
      data: { url: url, hd: 1 },
    },
    {
      name: "SSSTik API",
      url: "https://ssstik.io/abc",
      method: "POST",
      data: { id: url, locale: "en", tt: "RFBiZ3Bi" },
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        "HX-Request": "true",
        "HX-Target": "target",
        "HX-Current-URL": "https://ssstik.io/en",
      },
    },
    {
      name: "Tikmate API",
      url: "https://tikmate.online/download",
      method: "POST",
      data: { url: url },
    },
    {
      name: "Snaptik API",
      url: "https://snaptik.app/abc",
      method: "POST",
      data: { url: url, lang: "en" },
    },
    {
      name: "Musicaldown API",
      url: "https://musicaldown.com/download",
      method: "POST",
      data: { input: url, lang: "en" },
    },
  ];

  for (const api of apis) {
    try {
      console.log(`🔄 尝试 ${api.name}...`);

      let requestOptions = {
        method: api.method,
        headers: {
          "User-Agent": CONFIG.headers["User-Agent"],
          Accept: "application/json, text/html, */*",
          "Accept-Language": "en-US,en;q=0.9",
          Referer: api.url.split("/").slice(0, 3).join("/"),
          ...api.headers,
        },
        timeout: CONFIG.timeout,
      };

      // 根据API类型设置请求体
      if (api.name === "TikWM API") {
        requestOptions.headers["Content-Type"] = "application/json";
        requestOptions.body = JSON.stringify(api.data);
      } else if (api.name === "SSSTik API") {
        requestOptions.headers["Content-Type"] = "application/x-www-form-urlencoded";
        requestOptions.body = new URLSearchParams(api.data).toString();
      } else {
        requestOptions.headers["Content-Type"] = "application/x-www-form-urlencoded";
        requestOptions.body = new URLSearchParams(api.data).toString();
      }

      const response = await fetch(api.url, requestOptions);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const responseText = await response.text();

      // 根据不同API解析响应
      let result = null;

      if (api.name === "TikWM API") {
        try {
          const data = JSON.parse(responseText);
          if (data.code === 0 && data.data) {
            result = {
              method: api.name,
              success: true,
              data: {
                id: data.data.id,
                title: data.data.title,
                author: {
                  unique_id: data.data.author?.unique_id,
                  nickname: data.data.author?.nickname,
                  avatar: data.data.author?.avatar,
                },
                video: {
                  no_watermark: data.data.play,
                  with_watermark: data.data.wmplay,
                  cover: data.data.cover,
                  duration: data.data.duration,
                },
                stats: {
                  digg_count: data.data.digg_count,
                  comment_count: data.data.comment_count,
                  share_count: data.data.share_count,
                  play_count: data.data.play_count,
                },
              },
            };
          }
        } catch (e) {
          throw new Error("JSON解析失败");
        }
      } else {
        // 对于其他API，尝试从HTML中提取下载链接
        const videoUrlMatch = responseText.match(/https?:\/\/[^"'\s]+\.mp4[^"'\s]*/);
        const titleMatch = responseText.match(/<title[^>]*>([^<]+)/i);

        if (videoUrlMatch) {
          result = {
            method: api.name,
            success: true,
            data: {
              title: titleMatch ? titleMatch[1].trim() : "",
              video: {
                no_watermark: videoUrlMatch[0],
                url: videoUrlMatch[0],
              },
            },
          };
        }
      }

      if (result) {
        console.log(`✅ ${api.name} 解析成功`);
        return result;
      }
    } catch (error) {
      console.log(`❌ ${api.name} 失败:`, error.message);
    }
  }

  return { method: "Third Party API", success: false, error: "所有第三方API都失败了" };
}

/**
 * 主解析函数
 * 完全基于项目中的抖音解析逻辑
 */
async function parseTikTokVideo(inputText) {
  console.log("🚀 开始解析TikTok视频...");
  console.log("📎 输入内容:", inputText);
  console.log("🔍 解析策略: 基于项目抖音逻辑的网页解析");

  try {
    // 1. 提取TikTok链接
    const tiktokUrl = extractTikTokUrl(inputText);
    if (!tiktokUrl) {
      throw new Error("未找到有效的TikTok链接");
    }
    console.log("🔗 提取到的TikTok链接:", tiktokUrl);

    // 2. 解析TikTok视频信息
    const result = await parseTikTokShareUrl(tiktokUrl);

    console.log("🎉 TikTok解析成功！");
    return {
      success: true,
      method: "TikTok Web Parsing (Based on Douyin Logic)",
      data: result,
    };
  } catch (error) {
    console.log("💥 TikTok解析失败:", error.message);

    // 根据错误类型返回不同的错误信息
    let errorMessage = "TikTok视频解析失败";

    if (error.message.includes("未找到有效的TikTok链接")) {
      errorMessage = "未找到有效的TikTok链接，请检查输入内容";
    } else if (error.message.includes("HTTP请求失败")) {
      errorMessage = "网络请求失败，请稍后重试";
    } else if (error.message.includes("未获取到TikTok页面内容")) {
      errorMessage = "无法访问TikTok页面，可能是链接已失效、网络问题或地区限制";
    } else if (error.message.includes("TikTok数据解析失败")) {
      errorMessage = "TikTok数据解析失败，页面结构可能已变更";
    } else if (error.message.includes("无法从TikTok页面中提取视频信息")) {
      errorMessage = "无法从TikTok页面中提取视频信息，可能是视频已被删除或设为私密";
    }

    return {
      success: false,
      error: errorMessage,
      originalError: error.message,
    };
  }
}

/**
 * 格式化输出结果
 * 参考项目中的结果处理逻辑
 */
function formatResult(result) {
  console.log("\n" + "=".repeat(60));
  console.log("📊 TikTok解析结果");
  console.log("=".repeat(60));

  if (result.success) {
    console.log("✅ 解析成功");
    console.log("🔧 解析方法:", result.method);

    if (result.data) {
      const data = result.data;
      console.log("\n📹 视频信息:");
      if (data.title) console.log("  标题:", data.title);

      if (data.author) {
        console.log("\n👤 作者信息:");
        if (data.author.name) console.log("  昵称:", data.author.name);
        if (data.author.uid) console.log("  用户ID:", data.author.uid);
      }

      if (data.video_url) {
        console.log("\n🎬 视频链接:");
        console.log("  视频URL:", data.video_url);
      }

      if (data.cover_url) {
        console.log("  封面图片:", data.cover_url);
      }

      if (data.images && data.images.length > 0) {
        console.log("\n🖼️  图集模式:");
        console.log("  图片数量:", data.images.length);
      }
    }
  } else {
    console.log("❌ 解析失败");
    console.log("💥 错误信息:", result.error);
    if (result.originalError) {
      console.log("� 详细错误:", result.originalError);
    }
  }

  console.log("=".repeat(60));
}

/**
 * 测试函数
 */
async function runTests() {
  console.log("🧪 TikTok视频解析测试工具");
  console.log("📝 基于项目中抖音解析逻辑重新设计\n");

  const readline = require("readline");
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  });

  const askForInput = () => {
    rl.question("请输入TikTok链接或包含TikTok链接的文本 (输入 q 退出): ", async (input) => {
      const inputText = input.trim();

      if (inputText.toLowerCase() === "q") {
        console.log("👋 再见！");
        rl.close();
        return;
      }

      if (!inputText) {
        console.log("❌ 请输入有效的内容");
        askForInput();
        return;
      }

      console.log("\n🔄 开始解析...");
      const result = await parseTikTokVideo(inputText);
      formatResult(result);

      console.log("\n");
      askForInput();
    });
  };

  askForInput();
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

// 导出函数供其他模块使用
module.exports = {
  parseTikTokVideo,
  extractVideoId,
  resolveShortUrl,
};
