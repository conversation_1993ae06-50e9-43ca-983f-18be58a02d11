/**
 * TikTok视频解析测试脚本
 * 支持多种解析方法获取无水印视频链接
 * 
 * 使用方法:
 * node test-tiktok-parser.js
 * 
 * 依赖安装:
 * npm install node-fetch cheerio playwright
 */

const fetch = require('node-fetch');
const cheerio = require('cheerio');

// 配置
const CONFIG = {
  // 请求头配置
  headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Cache-Control': 'max-age=0'
  },
  
  // 超时设置
  timeout: 15000,
  
  // 重试次数
  maxRetries: 3
};

/**
 * 延迟函数
 */
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 提取TikTok视频ID
 */
function extractVideoId(url) {
  console.log('🔍 提取视频ID:', url);
  
  // 支持多种TikTok URL格式
  const patterns = [
    /\/video\/(\d+)/,
    /\/photo\/(\d+)/,
    /\/t\/([A-Za-z0-9]+)/,
    /vm\.tiktok\.com\/([A-Za-z0-9]+)/,
    /vt\.tiktok\.com\/([A-Za-z0-9]+)/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      console.log('✅ 找到视频ID:', match[1]);
      return match[1];
    }
  }
  
  console.log('❌ 无法提取视频ID');
  return null;
}

/**
 * 处理短链接重定向
 */
async function resolveShortUrl(url) {
  if (!url.includes('vm.tiktok.com') && !url.includes('vt.tiktok.com')) {
    return url;
  }
  
  console.log('🔄 处理短链接重定向...');
  
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      headers: CONFIG.headers,
      redirect: 'manual',
      timeout: CONFIG.timeout
    });
    
    const location = response.headers.get('location');
    if (location) {
      console.log('✅ 重定向到:', location);
      return location;
    }
  } catch (error) {
    console.log('⚠️ 重定向失败，使用原URL:', error.message);
  }
  
  return url;
}

/**
 * 方法1: 使用TikTok官方API
 */
async function parseWithOfficialAPI(videoId) {
  console.log('📡 方法1: 尝试官方API解析...');
  
  const apiUrl = `https://api22-normal-c-alisg.tiktokv.com/aweme/v1/feed/?aweme_id=${videoId}&iid=7318518857994389254&device_id=7318517321748022790&channel=googleplay&app_name=musical_ly&version_code=300904&device_platform=android&device_type=ASUS_Z01QD&version=9`;
  
  try {
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        ...CONFIG.headers,
        'X-Requested-With': 'XMLHttpRequest'
      },
      timeout: CONFIG.timeout
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const data = await response.json();
    
    if (data.aweme_list && data.aweme_list.length > 0) {
      const video = data.aweme_list[0];
      
      const result = {
        method: 'Official API',
        success: true,
        data: {
          id: video.aweme_id,
          desc: video.desc || '',
          author: {
            unique_id: video.author?.unique_id || '',
            nickname: video.author?.nickname || '',
            avatar: video.author?.avatar_thumb?.url_list?.[0] || ''
          },
          video: {
            // 无水印链接
            play_addr: video.video?.play_addr?.url_list?.[0] || '',
            // 有水印链接
            download_addr: video.video?.download_addr?.url_list?.[0] || '',
            cover: video.video?.cover?.url_list?.[0] || '',
            duration: video.video?.duration || 0
          },
          // 图集模式
          images: video.image_post_info?.images?.map(img => 
            img.display_image?.url_list?.[1] || img.display_image?.url_list?.[0]
          ) || [],
          stats: {
            digg_count: video.statistics?.digg_count || 0,
            comment_count: video.statistics?.comment_count || 0,
            share_count: video.statistics?.share_count || 0,
            play_count: video.statistics?.play_count || 0
          }
        }
      };
      
      console.log('✅ 官方API解析成功');
      return result;
    }
    
    throw new Error('API返回数据为空');
    
  } catch (error) {
    console.log('❌ 官方API解析失败:', error.message);
    return { method: 'Official API', success: false, error: error.message };
  }
}

/**
 * 方法2: 网页解析
 */
async function parseWithWebScraping(url) {
  console.log('🌐 方法2: 尝试网页解析...');
  
  try {
    const response = await fetch(url, {
      headers: CONFIG.headers,
      timeout: CONFIG.timeout
    });
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
    
    const html = await response.text();
    const $ = cheerio.load(html);
    
    // 尝试从script标签中提取数据
    let jsonData = null;
    
    $('script').each((i, elem) => {
      const content = $(elem).html();
      if (content && content.includes('__UNIVERSAL_DATA_FOR_REHYDRATION__')) {
        try {
          const match = content.match(/__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*(.+?)<\/script>/s);
          if (match) {
            jsonData = JSON.parse(match[1]);
          }
        } catch (e) {
          // 继续尝试其他script标签
        }
      }
    });
    
    if (jsonData) {
      // 尝试从不同路径提取视频信息
      const possiblePaths = [
        'default.webapp.video-detail',
        'default.ItemModule',
        'ItemModule'
      ];
      
      for (const path of possiblePaths) {
        let current = jsonData;
        const parts = path.split('.');
        
        for (const part of parts) {
          if (current && current[part]) {
            current = current[part];
          } else {
            current = null;
            break;
          }
        }
        
        if (current && current.itemInfo) {
          const video = current.itemInfo.itemStruct;
          
          const result = {
            method: 'Web Scraping',
            success: true,
            data: {
              id: video.id,
              desc: video.desc || '',
              author: {
                unique_id: video.author?.uniqueId || '',
                nickname: video.author?.nickname || '',
                avatar: video.author?.avatarThumb || ''
              },
              video: {
                play_addr: video.video?.playAddr || '',
                download_addr: video.video?.downloadAddr || '',
                cover: video.video?.cover || '',
                duration: video.video?.duration || 0
              },
              images: video.imagePost?.images?.map(img => img.imageURL?.urlList?.[0]) || [],
              stats: {
                digg_count: video.stats?.diggCount || 0,
                comment_count: video.stats?.commentCount || 0,
                share_count: video.stats?.shareCount || 0,
                play_count: video.stats?.playCount || 0
              }
            }
          };
          
          console.log('✅ 网页解析成功');
          return result;
        }
      }
    }
    
    // 降级方案：从meta标签提取基本信息
    const title = $('title').text() || '';
    const description = $('meta[name="description"]').attr('content') || '';
    const videoUrl = $('meta[property="og:video"]').attr('content') || '';
    const image = $('meta[property="og:image"]').attr('content') || '';
    
    if (title || videoUrl) {
      console.log('✅ 网页解析成功（降级方案）');
      return {
        method: 'Web Scraping (Fallback)',
        success: true,
        data: {
          title: title.replace(/\s*\|\s*TikTok$/, '').trim(),
          description,
          video: { play_addr: videoUrl },
          cover: image
        }
      };
    }
    
    throw new Error('无法从网页中提取视频信息');
    
  } catch (error) {
    console.log('❌ 网页解析失败:', error.message);
    return { method: 'Web Scraping', success: false, error: error.message };
  }
}

/**
 * 方法3: 第三方API解析
 */
async function parseWithThirdPartyAPI(url) {
  console.log('🔌 方法3: 尝试第三方API解析...');
  
  // 这里可以集成一些第三方TikTok解析API
  // 例如: tikwm.com, ssstik.io 等
  
  const apis = [
    {
      name: 'TikWM API',
      url: 'https://www.tikwm.com/api/',
      method: 'POST',
      data: { url: url, hd: 1 }
    }
  ];
  
  for (const api of apis) {
    try {
      console.log(`🔄 尝试 ${api.name}...`);
      
      const response = await fetch(api.url, {
        method: api.method,
        headers: {
          'Content-Type': 'application/json',
          ...CONFIG.headers
        },
        body: JSON.stringify(api.data),
        timeout: CONFIG.timeout
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.code === 0 && data.data) {
        console.log(`✅ ${api.name} 解析成功`);
        return {
          method: api.name,
          success: true,
          data: {
            id: data.data.id,
            title: data.data.title,
            author: {
              unique_id: data.data.author?.unique_id,
              nickname: data.data.author?.nickname,
              avatar: data.data.author?.avatar
            },
            video: {
              play_addr: data.data.play,
              download_addr: data.data.wmplay,
              cover: data.data.cover,
              duration: data.data.duration
            },
            stats: {
              digg_count: data.data.digg_count,
              comment_count: data.data.comment_count,
              share_count: data.data.share_count,
              play_count: data.data.play_count
            }
          }
        };
      }
      
    } catch (error) {
      console.log(`❌ ${api.name} 失败:`, error.message);
    }
  }
  
  return { method: 'Third Party API', success: false, error: '所有第三方API都失败了' };
}

/**
 * 主解析函数
 */
async function parseTikTokVideo(url) {
  console.log('🚀 开始解析TikTok视频...');
  console.log('📎 输入URL:', url);
  
  try {
    // 1. 处理短链接
    const resolvedUrl = await resolveShortUrl(url);
    
    // 2. 提取视频ID
    const videoId = extractVideoId(resolvedUrl);
    if (!videoId) {
      throw new Error('无法提取视频ID');
    }
    
    // 3. 尝试多种解析方法
    const methods = [
      () => parseWithOfficialAPI(videoId),
      () => parseWithWebScraping(resolvedUrl),
      () => parseWithThirdPartyAPI(resolvedUrl)
    ];
    
    const results = [];
    
    for (const method of methods) {
      try {
        const result = await method();
        results.push(result);
        
        if (result.success) {
          console.log('🎉 解析成功！');
          return result;
        }
        
        // 失败时等待一下再尝试下一个方法
        await delay(1000);
        
      } catch (error) {
        console.log('⚠️ 方法执行异常:', error.message);
        results.push({ success: false, error: error.message });
      }
    }
    
    // 所有方法都失败了
    console.log('💥 所有解析方法都失败了');
    return {
      success: false,
      error: '所有解析方法都失败了',
      attempts: results
    };
    
  } catch (error) {
    console.log('💥 解析过程出错:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试函数
 */
async function runTests() {
  console.log('🧪 TikTok视频解析测试开始\n');
  
  // 测试URL列表
  const testUrls = [
    'https://www.tiktok.com/@username/video/1234567890123456789',
    'https://vm.tiktok.com/ZMhQQQQQQ/',
    'https://vt.tiktok.com/ZSjjjjjjj/'
  ];
  
  console.log('📝 请输入要测试的TikTok URL，或直接回车使用示例URL:');
  
  // 简单的命令行输入
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('TikTok URL: ', async (inputUrl) => {
    const urlToTest = inputUrl.trim() || testUrls[0];
    
    console.log('\n' + '='.repeat(60));
    console.log(`测试URL: ${urlToTest}`);
    console.log('='.repeat(60) + '\n');
    
    const result = await parseTikTokVideo(urlToTest);
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 解析结果:');
    console.log('='.repeat(60));
    console.log(JSON.stringify(result, null, 2));
    
    rl.close();
  });
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

// 导出函数供其他模块使用
module.exports = {
  parseTikTokVideo,
  extractVideoId,
  resolveShortUrl
};
