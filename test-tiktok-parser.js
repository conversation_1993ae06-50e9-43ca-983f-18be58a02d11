/**
 * TikTok视频解析测试脚本
 * 支持多种解析方法获取无水印视频链接
 * 
 * 使用方法:
 * node test-tiktok-parser.js
 * 
 * 依赖安装:
 * npm install node-fetch cheerio playwright
 */

const fetch = require('node-fetch');
const cheerio = require('cheerio');

// 配置
const CONFIG = {
  // 请求头配置
  headers: {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'DNT': '1',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1',
    'Sec-Fetch-Dest': 'document',
    'Sec-Fetch-Mode': 'navigate',
    'Sec-Fetch-Site': 'none',
    'Cache-Control': 'max-age=0'
  },
  
  // 超时设置
  timeout: 15000,
  
  // 重试次数
  maxRetries: 3
};

/**
 * 延迟函数
 */
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 提取TikTok视频ID
 */
function extractVideoId(url) {
  console.log('🔍 提取视频ID:', url);
  
  // 支持多种TikTok URL格式
  const patterns = [
    /\/video\/(\d+)/,
    /\/photo\/(\d+)/,
    /\/t\/([A-Za-z0-9]+)/,
    /vm\.tiktok\.com\/([A-Za-z0-9]+)/,
    /vt\.tiktok\.com\/([A-Za-z0-9]+)/
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      console.log('✅ 找到视频ID:', match[1]);
      return match[1];
    }
  }
  
  console.log('❌ 无法提取视频ID');
  return null;
}

/**
 * 处理短链接重定向
 */
async function resolveShortUrl(url) {
  if (!url.includes('vm.tiktok.com') && !url.includes('vt.tiktok.com')) {
    return url;
  }
  
  console.log('🔄 处理短链接重定向...');
  
  try {
    const response = await fetch(url, {
      method: 'HEAD',
      headers: CONFIG.headers,
      redirect: 'manual',
      timeout: CONFIG.timeout
    });
    
    const location = response.headers.get('location');
    if (location) {
      console.log('✅ 重定向到:', location);
      return location;
    }
  } catch (error) {
    console.log('⚠️ 重定向失败，使用原URL:', error.message);
  }
  
  return url;
}

/**
 * 方法1: 桌面端网页解析
 */
async function parseWithDesktopWeb(url) {
  console.log('�️ 方法1: 桌面端网页解析...');

  try {
    const response = await fetch(url, {
      headers: {
        ...CONFIG.headers,
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      },
      timeout: CONFIG.timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();

    // 提取页面中的JSON数据
    const jsonPatterns = [
      // 主要数据源
      /__UNIVERSAL_DATA_FOR_REHYDRATION__\s*=\s*({.+?})\s*<\/script>/s,
      // 备用数据源
      /window\.__INITIAL_STATE__\s*=\s*({.+?});/s,
      /SIGI_STATE\s*=\s*({.+?});/s,
      /"ItemModule":\s*({.+?})/s
    ];

    for (const pattern of jsonPatterns) {
      const match = html.match(pattern);
      if (match) {
        try {
          const jsonData = JSON.parse(match[1]);
          console.log('✅ 找到JSON数据，开始解析...');

          const videoData = extractVideoFromDesktopJson(jsonData);
          if (videoData) {
            console.log('✅ 桌面端解析成功');
            return {
              method: 'Desktop Web Parsing',
              success: true,
              data: videoData
            };
          }
        } catch (e) {
          console.log('⚠️ JSON解析失败，尝试下一个模式');
          continue;
        }
      }
    }

    // 降级方案：从meta标签提取基本信息
    const basicInfo = extractBasicInfoFromHtml(html);
    if (basicInfo) {
      console.log('✅ 桌面端基本信息解析成功');
      return {
        method: 'Desktop Web Basic',
        success: true,
        data: basicInfo
      };
    }

    throw new Error('无法从桌面端网页提取视频信息');

  } catch (error) {
    console.log('❌ 桌面端解析失败:', error.message);
    return { method: 'Desktop Web Parsing', success: false, error: error.message };
  }
}

/**
 * 从桌面端JSON数据中提取视频信息
 */
function extractVideoFromDesktopJson(jsonData) {
  try {
    // 尝试不同的数据路径
    const paths = [
      'default.webapp.video-detail.itemInfo.itemStruct',
      'default.ItemModule',
      'ItemModule'
    ];

    for (const path of paths) {
      let current = jsonData;
      const parts = path.split('.');

      for (const part of parts) {
        if (current && current[part]) {
          current = current[part];
        } else {
          current = null;
          break;
        }
      }

      if (current && current.itemInfo) {
        const video = current.itemInfo.itemStruct;

        return {
          id: video.id,
          desc: video.desc || '',
          author: {
            unique_id: video.author?.uniqueId || '',
            nickname: video.author?.nickname || '',
            avatar: video.author?.avatarThumb || ''
          },
          video: {
            // 无水印链接
            no_watermark: video.video?.playAddr || '',
            // 有水印链接
            with_watermark: video.video?.downloadAddr || '',
            cover: video.video?.cover || '',
            duration: video.video?.duration || 0
          },
          // 图集模式
          images: video.imagePost?.images?.map(img => img.imageURL?.urlList?.[0]) || [],
          stats: {
            digg_count: video.stats?.diggCount || 0,
            comment_count: video.stats?.commentCount || 0,
            share_count: video.stats?.shareCount || 0,
            play_count: video.stats?.playCount || 0
          }
        };
      }
    }

    return null;
  } catch (error) {
    console.log('⚠️ 桌面端JSON数据提取失败:', error.message);
    return null;
  }
}

/**
 * 从HTML中提取基本信息
 */
function extractBasicInfoFromHtml(html) {
  try {
    // 提取meta标签信息
    const titleMatch = html.match(/<title[^>]*>(.*?)<\/title>/i);
    const title = titleMatch ? titleMatch[1].replace(/\s*\|\s*TikTok$/, '').trim() : '';

    const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']*)/i);
    const description = descMatch ? descMatch[1] : '';

    const videoUrlMatch = html.match(/<meta[^>]*property=["']og:video["'][^>]*content=["']([^"']*)/i);
    const videoUrl = videoUrlMatch ? videoUrlMatch[1] : '';

    const imageMatch = html.match(/<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']*)/i);
    const image = imageMatch ? imageMatch[1] : '';

    if (title || videoUrl) {
      return {
        title: title,
        description: description,
        video: {
          url: videoUrl,
          cover: image
        }
      };
    }

    return null;
  } catch (error) {
    console.log('⚠️ 基本信息提取失败:', error.message);
    return null;
  }
}

/**
 * 方法2: 移动端网页解析
 */
async function parseWithMobileWeb(url) {
  console.log('📱 方法2: 移动端网页解析...');

  try {
    // 构造移动端URL
    const mobileUrl = url.replace('www.tiktok.com', 'm.tiktok.com');

    const response = await fetch(mobileUrl, {
      headers: {
        ...CONFIG.headers,
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'en-US,en;q=0.5'
      },
      timeout: CONFIG.timeout
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const html = await response.text();

    // 移动端特有的JSON数据模式
    const mobileJsonPatterns = [
      /window\.__INITIAL_STATE__\s*=\s*({.+?});/s,
      /__NEXT_DATA__\s*=\s*({.+?})\s*<\/script>/s,
      /SIGI_STATE\s*=\s*({.+?});/s
    ];

    for (const pattern of mobileJsonPatterns) {
      const match = html.match(pattern);
      if (match) {
        try {
          const jsonData = JSON.parse(match[1]);
          console.log('✅ 找到移动端JSON数据');

          const videoData = extractVideoFromMobileJson(jsonData);
          if (videoData) {
            console.log('✅ 移动端解析成功');
            return {
              method: 'Mobile Web Parsing',
              success: true,
              data: videoData
            };
          }
        } catch (e) {
          console.log('⚠️ 移动端JSON解析失败，尝试下一个模式');
          continue;
        }
      }
    }

    // 移动端降级方案
    const basicInfo = extractBasicInfoFromHtml(html);
    if (basicInfo) {
      console.log('✅ 移动端基本信息解析成功');
      return {
        method: 'Mobile Web Basic',
        success: true,
        data: basicInfo
      };
    }

    throw new Error('无法从移动端网页提取视频信息');

  } catch (error) {
    console.log('❌ 移动端解析失败:', error.message);
    return { method: 'Mobile Web Parsing', success: false, error: error.message };
  }
}

/**
 * 从移动端JSON数据中提取视频信息
 */
function extractVideoFromMobileJson(jsonData) {
  try {
    // 移动端数据路径
    const paths = [
      'props.pageProps.itemInfo.itemStruct',
      'props.pageProps.videoData',
      'itemInfo.itemStruct',
      'videoData'
    ];

    for (const path of paths) {
      let current = jsonData;
      const parts = path.split('.');

      for (const part of parts) {
        if (current && current[part]) {
          current = current[part];
        } else {
          current = null;
          break;
        }
      }

      if (current && (current.id || current.aweme_id)) {
        const video = current;

        return {
          id: video.id || video.aweme_id,
          desc: video.desc || video.description || '',
          author: {
            unique_id: video.author?.uniqueId || video.author?.unique_id || '',
            nickname: video.author?.nickname || '',
            avatar: video.author?.avatarThumb || video.author?.avatar_thumb?.url_list?.[0] || ''
          },
          video: {
            no_watermark: video.video?.playAddr || video.video?.play_addr?.url_list?.[0] || '',
            with_watermark: video.video?.downloadAddr || video.video?.download_addr?.url_list?.[0] || '',
            cover: video.video?.cover || video.video?.cover?.url_list?.[0] || '',
            duration: video.video?.duration || 0
          },
          images: video.imagePost?.images?.map(img => img.imageURL?.urlList?.[0]) ||
                   video.image_post_info?.images?.map(img => img.display_image?.url_list?.[0]) || [],
          stats: {
            digg_count: video.stats?.diggCount || video.statistics?.digg_count || 0,
            comment_count: video.stats?.commentCount || video.statistics?.comment_count || 0,
            share_count: video.stats?.shareCount || video.statistics?.share_count || 0,
            play_count: video.stats?.playCount || video.statistics?.play_count || 0
          }
        };
      }
    }

    return null;
  } catch (error) {
    console.log('⚠️ 移动端JSON数据提取失败:', error.message);
    return null;
  }
}

/**
 * 方法3: 第三方API解析 (2024-2025最新)
 */
async function parseWithThirdPartyAPI(url) {
  console.log('🔌 方法3: 尝试第三方API解析...');

  // 基于最新搜索结果的第三方API服务
  const apis = [
    {
      name: 'TikWM API',
      url: 'https://www.tikwm.com/api/',
      method: 'POST',
      data: { url: url, hd: 1 }
    },
    {
      name: 'SSSTik API',
      url: 'https://ssstik.io/abc',
      method: 'POST',
      data: { id: url, locale: 'en', tt: 'RFBiZ3Bi' },
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'HX-Request': 'true',
        'HX-Target': 'target',
        'HX-Current-URL': 'https://ssstik.io/en'
      }
    },
    {
      name: 'Tikmate API',
      url: 'https://tikmate.online/download',
      method: 'POST',
      data: { url: url }
    },
    {
      name: 'Snaptik API',
      url: 'https://snaptik.app/abc',
      method: 'POST',
      data: { url: url, lang: 'en' }
    },
    {
      name: 'Musicaldown API',
      url: 'https://musicaldown.com/download',
      method: 'POST',
      data: { input: url, lang: 'en' }
    }
  ];

  for (const api of apis) {
    try {
      console.log(`🔄 尝试 ${api.name}...`);

      let requestOptions = {
        method: api.method,
        headers: {
          'User-Agent': CONFIG.headers['User-Agent'],
          'Accept': 'application/json, text/html, */*',
          'Accept-Language': 'en-US,en;q=0.9',
          'Referer': api.url.split('/').slice(0, 3).join('/'),
          ...api.headers
        },
        timeout: CONFIG.timeout
      };

      // 根据API类型设置请求体
      if (api.name === 'TikWM API') {
        requestOptions.headers['Content-Type'] = 'application/json';
        requestOptions.body = JSON.stringify(api.data);
      } else if (api.name === 'SSSTik API') {
        requestOptions.headers['Content-Type'] = 'application/x-www-form-urlencoded';
        requestOptions.body = new URLSearchParams(api.data).toString();
      } else {
        requestOptions.headers['Content-Type'] = 'application/x-www-form-urlencoded';
        requestOptions.body = new URLSearchParams(api.data).toString();
      }

      const response = await fetch(api.url, requestOptions);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      const responseText = await response.text();

      // 根据不同API解析响应
      let result = null;

      if (api.name === 'TikWM API') {
        try {
          const data = JSON.parse(responseText);
          if (data.code === 0 && data.data) {
            result = {
              method: api.name,
              success: true,
              data: {
                id: data.data.id,
                title: data.data.title,
                author: {
                  unique_id: data.data.author?.unique_id,
                  nickname: data.data.author?.nickname,
                  avatar: data.data.author?.avatar
                },
                video: {
                  no_watermark: data.data.play,
                  with_watermark: data.data.wmplay,
                  cover: data.data.cover,
                  duration: data.data.duration
                },
                stats: {
                  digg_count: data.data.digg_count,
                  comment_count: data.data.comment_count,
                  share_count: data.data.share_count,
                  play_count: data.data.play_count
                }
              }
            };
          }
        } catch (e) {
          throw new Error('JSON解析失败');
        }
      } else {
        // 对于其他API，尝试从HTML中提取下载链接
        const videoUrlMatch = responseText.match(/https?:\/\/[^"'\s]+\.mp4[^"'\s]*/);
        const titleMatch = responseText.match(/<title[^>]*>([^<]+)/i);

        if (videoUrlMatch) {
          result = {
            method: api.name,
            success: true,
            data: {
              title: titleMatch ? titleMatch[1].trim() : '',
              video: {
                no_watermark: videoUrlMatch[0],
                url: videoUrlMatch[0]
              }
            }
          };
        }
      }

      if (result) {
        console.log(`✅ ${api.name} 解析成功`);
        return result;
      }

    } catch (error) {
      console.log(`❌ ${api.name} 失败:`, error.message);
    }
  }

  return { method: 'Third Party API', success: false, error: '所有第三方API都失败了' };
}

/**
 * 主解析函数 (纯网页解析，不使用API)
 */
async function parseTikTokVideo(url) {
  console.log('🚀 开始解析TikTok视频...');
  console.log('📎 输入URL:', url);
  console.log('🔍 解析策略: 纯网页解析 (不使用API接口)');

  try {
    // 1. 处理短链接
    const resolvedUrl = await resolveShortUrl(url);
    console.log('🔗 解析后URL:', resolvedUrl);

    // 2. 提取视频ID
    const videoId = extractVideoId(resolvedUrl);
    if (!videoId) {
      throw new Error('无法提取视频ID');
    }
    console.log('🆔 视频ID:', videoId);

    // 3. 尝试多种网页解析方法
    const methods = [
      () => parseWithDesktopWeb(resolvedUrl),
      () => parseWithMobileWeb(resolvedUrl),
      () => parseWithThirdPartyAPI(resolvedUrl)
    ];

    const results = [];

    for (const method of methods) {
      try {
        const result = await method();
        results.push(result);

        if (result.success) {
          console.log('🎉 解析成功！');
          return result;
        }

        // 失败时等待一下再尝试下一个方法
        console.log('⏳ 等待1秒后尝试下一个方法...');
        await delay(1000);

      } catch (error) {
        console.log('⚠️ 方法执行异常:', error.message);
        results.push({ success: false, error: error.message });
      }
    }

    // 所有方法都失败了
    console.log('💥 所有解析方法都失败了');
    return {
      success: false,
      error: '所有解析方法都失败了',
      attempts: results
    };

  } catch (error) {
    console.log('💥 解析过程出错:', error.message);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * 测试函数
 */
async function runTests() {
  console.log('🧪 TikTok视频解析测试开始\n');
  
  // 测试URL列表
  const testUrls = [
    'https://www.tiktok.com/@username/video/1234567890123456789',
    'https://vm.tiktok.com/ZMhQQQQQQ/',
    'https://vt.tiktok.com/ZSjjjjjjj/'
  ];
  
  console.log('📝 请输入要测试的TikTok URL，或直接回车使用示例URL:');
  
  // 简单的命令行输入
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });
  
  rl.question('TikTok URL: ', async (inputUrl) => {
    const urlToTest = inputUrl.trim() || testUrls[0];
    
    console.log('\n' + '='.repeat(60));
    console.log(`测试URL: ${urlToTest}`);
    console.log('='.repeat(60) + '\n');
    
    const result = await parseTikTokVideo(urlToTest);
    
    console.log('\n' + '='.repeat(60));
    console.log('📊 解析结果:');
    console.log('='.repeat(60));
    console.log(JSON.stringify(result, null, 2));
    
    rl.close();
  });
}

// 如果直接运行此脚本
if (require.main === module) {
  runTests().catch(console.error);
}

// 导出函数供其他模块使用
module.exports = {
  parseTikTokVideo,
  extractVideoId,
  resolveShortUrl
};
