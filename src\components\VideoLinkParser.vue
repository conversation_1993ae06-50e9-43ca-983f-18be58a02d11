<template>
  <view class="video-link-parser">
    <view class="parser-header">
      <view class="parser-icon">🔗</view>
      <view class="parser-info">
        <text class="parser-title">视频链接解析</text>
        <text class="parser-desc">支持抖音、小红书视频链接，自动获取无水印版本</text>
      </view>
    </view>

    <view class="input-section">
      <view class="input-container">
        <textarea
          v-model="linkInput"
          class="link-input"
          placeholder="请粘贴抖音或小红书的分享内容（支持链接或完整分享文本）..."
          :disabled="isProcessing"
          @input="handleInputChange"
          @blur="handleInputBlur"
        />
        <view v-if="linkInput" class="clear-btn" @click="clearInput">
          <text class="clear-icon">✕</text>
        </view>
      </view>

      <view class="input-tips">
        <text class="tip-text">💡 支持的内容格式:</text>
        <text class="tip-item">• 抖音分享链接: https://v.douyin.com/xxx/</text>
        <text class="tip-item">• 抖音分享文本: 复制打开抖音，看看【用户名的作品】...</text>
        <text class="tip-item">• 小红书分享链接: https://xiaohongshu.com/xxx</text>
      </view>
    </view>

    <view class="action-section">
      <button
        class="parse-btn"
        :class="{ 'btn-disabled': !canParse, 'btn-loading': isProcessing }"
        :disabled="!canParse || isProcessing"
        @click="handleParseClick"
      >
        <text v-if="!isProcessing" class="btn-text">解析视频</text>
        <view v-else class="loading-content">
          <text class="loading-icon">⏳</text>
          <text class="btn-text">{{ processingStatus }}</text>
        </view>
      </button>
    </view>

    <!-- 解析进度显示 -->
    <view v-if="isProcessing" class="progress-section">
      <view class="progress-steps">
        <view
          v-for="(step, index) in progressSteps"
          :key="index"
          class="progress-step"
          :class="{
            'step-active': index === currentStepIndex,
            'step-completed': index < currentStepIndex,
            'step-pending': index > currentStepIndex,
          }"
        >
          <view class="step-indicator">
            <text v-if="index < currentStepIndex" class="step-icon">✓</text>
            <text v-else-if="index === currentStepIndex" class="step-loading">⏳</text>
            <text v-else class="step-number">{{ index + 1 }}</text>
          </view>
          <text class="step-text">{{ step }}</text>
        </view>
      </view>
    </view>

    <!-- 解析结果预览 -->
    <view v-if="parseResult" class="result-section">
      <view class="result-header">
        <text class="result-title">解析成功！</text>
        <text class="result-desc">视频信息已获取，点击确认继续处理</text>
      </view>

      <view class="video-preview">
        <!-- 视频封面和基本信息的卡片式布局 -->
        <view class="preview-card">
          <view class="preview-left">
            <!-- 视频封面 -->
            <view v-if="parseResult.thumbnailUrl" class="video-cover">
              <image
                :src="parseResult.thumbnailUrl"
                class="cover-image"
                mode="aspectFill"
                @error="handleCoverError"
              />
              <view class="cover-overlay">
                <view class="play-icon">▶</view>
              </view>
            </view>
            <view v-else class="video-cover-placeholder">
              <view class="placeholder-icon">📹</view>
              <text class="placeholder-text">暂无封面</text>
            </view>
          </view>

          <view class="preview-right">
            <view class="video-info">
              <view class="info-title">
                <text class="title-text">{{ parseResult.title || "无标题" }}</text>
              </view>

              <view class="info-meta">
                <view class="meta-item">
                  <view class="meta-icon">👤</view>
                  <text class="meta-text">{{ parseResult.author?.name || "未知作者" }}</text>
                </view>

                <view class="meta-badges">
                  <view class="platform-badge" :class="parseResult.platform">
                    <text class="platform-text">
                      {{ parseResult.platform === "douyin" ? "抖音" : "小红书" }}
                    </text>
                  </view>
                  <view class="quality-tag">
                    <text class="quality-text">无水印高清</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <view class="result-actions">
        <button class="retry-btn" @click="resetParser">重新解析</button>
        <button class="confirm-btn" @click="confirmParsedVideo">确认使用</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from "vue";

// Props
interface Props {
  disabled?: boolean;
  userInfo?: {
    openid?: string;
    userId?: string;
    isLogin?: boolean;
  };
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  userInfo: () => ({ isLogin: false }),
});

// Emits
interface Emits {
  (e: "parsed", result: ParsedVideoResult): void;
  (e: "error", error: string): void;
}

const emit = defineEmits<Emits>();

// 解析结果接口
interface ParsedVideoResult {
  platform: "douyin" | "xiaohongshu";
  title: string;
  videoUrl: string;
  thumbnailUrl?: string;
  originalLink: string;
  author?: {
    name: string;
    uid: string;
    avatar: string;
  };
}

// 响应式数据
const linkInput = ref("");
const isProcessing = ref(false);
const processingStatus = ref("");
const parseResult = ref<ParsedVideoResult | null>(null);
const currentStepIndex = ref(0);

// 解析步骤 - 动态显示不同平台的处理流程
const progressSteps = ref<string[]>(["识别内容格式", "解析视频链接", "获取无水印源", "处理完成"]);

// 不同平台的解析步骤
const platformSteps = {
  douyin: ["识别抖音分享内容", "提取真实视频链接", "获取无水印视频源", "解析完成"],
  xiaohongshu: ["识别小红书分享链接", "解析视频详情信息", "获取高清视频源", "解析完成"],
  tiktok: ["识别TikTok分享链接", "解析视频详情信息", "获取无水印视频源", "解析完成"],
  unknown: ["分析输入内容格式", "尝试识别视频平台", "获取视频信息", "处理完成"],
};

// 计算属性
const canParse = computed(() => {
  return linkInput.value.trim().length > 0 && !props.disabled && !isProcessing.value;
});

// 检测链接平台（增强版，支持分享文本）
const detectPlatform = (inputText: string): "douyin" | "xiaohongshu" | "tiktok" | null => {
  const cleanText = inputText.toLowerCase().trim();

  // 抖音检测特征
  const douyinIndicators = ["复制打开抖音", "v.douyin.com", "www.douyin.com", "抖音", "douyin"];

  // 小红书检测特征
  const xiaohongshuIndicators = ["xiaohongshu.com", "xhslink.com", "小红书", "redbook"];

  // TikTok检测特征
  const tiktokIndicators = ["tiktok.com", "vm.tiktok.com", "vt.tiktok.com", "tiktok", "抖音国际版"];

  // 检测抖音
  for (const indicator of douyinIndicators) {
    if (cleanText.includes(indicator)) {
      return "douyin";
    }
  }

  // 检测小红书
  for (const indicator of xiaohongshuIndicators) {
    if (cleanText.includes(indicator)) {
      return "xiaohongshu";
    }
  }

  // 检测TikTok
  for (const indicator of tiktokIndicators) {
    if (cleanText.includes(indicator)) {
      return "tiktok";
    }
  }

  return null;
};

// 处理输入变化
const handleInputChange = () => {
  if (parseResult.value) {
    parseResult.value = null;
  }
};

// 处理输入失焦
const handleInputBlur = () => {
  linkInput.value = linkInput.value.trim();
};

// 清空输入
const clearInput = () => {
  linkInput.value = "";
  parseResult.value = null;
};

// 重置解析器
const resetParser = () => {
  linkInput.value = "";
  parseResult.value = null;
  isProcessing.value = false;
  processingStatus.value = "";
  currentStepIndex.value = 0;
};

// 处理解析点击
const handleParseClick = async () => {
  if (!canParse.value) return;

  // 检查用户登录状态
  if (!props.userInfo?.isLogin || (!props.userInfo?.openid && !props.userInfo?.userId)) {
    emit("error", "请先登录后再使用链接解析功能");
    return;
  }

  const platform = detectPlatform(linkInput.value);
  if (!platform) {
    emit("error", "无法识别内容格式，请确认输入的是抖音、小红书或TikTok的分享内容");
    return;
  }

  await startParsing(platform);
};

// 开始解析
const startParsing = async (platform: "douyin" | "xiaohongshu" | "tiktok") => {
  isProcessing.value = true;
  currentStepIndex.value = 0;

  // 根据平台设置对应的解析步骤
  progressSteps.value = platformSteps[platform];
  processingStatus.value = progressSteps.value[0] + "...";

  try {
    // 步骤1: 识别平台和内容格式
    await sleep(800);
    currentStepIndex.value = 1;
    processingStatus.value = progressSteps.value[1] + "...";

    // 调用云函数解析视频
    const result = await uniCloud.callFunction({
      name: "parse-video-link",
      data: {
        url: linkInput.value.trim(), // 发送完整的输入内容（可能是链接或分享文本）
        openid: props.userInfo?.openid,
        userId: props.userInfo?.userId,
        platform: platform, // 传递识别到的平台信息
      },
    });

    if (result.result.code !== 0) {
      // 创建包含详细错误信息的错误对象
      const errorInfo = {
        message: result.result.message || "解析失败",
        error: result.result.error || "",
        data: result.result.data || null
      };

      // 抛出包含完整错误信息的错误
      const error = new Error(errorInfo.message) as any;
      error.cloudFunctionResult = result.result;
      throw error;
    }

    // 步骤2: 解析链接完成，开始获取视频源
    await sleep(600);
    currentStepIndex.value = 2;
    processingStatus.value = progressSteps.value[2] + "...";

    // 根据平台调整处理时间（抖音通常更快，TikTok可能需要更长时间）
    const processTime = platform === "douyin" ? 1500 : platform === "tiktok" ? 2800 : 2200;
    await sleep(processTime);

    currentStepIndex.value = 3;
    processingStatus.value = progressSteps.value[3];

    await sleep(500);

    // 设置解析结果 - 使用云函数返回的实际数据结构
    parseResult.value = {
      platform: result.result.data.platform,
      title: result.result.data.title,
      videoUrl: result.result.data.video.url,
      thumbnailUrl: result.result.data.video.cover,
      originalLink: linkInput.value.trim(),
      author: result.result.data.author,
    };
  } catch (error: any) {
    // 优先使用云函数返回的具体错误信息
    let errorMessage = "解析失败，请检查链接是否正确";

    if (error.cloudFunctionResult) {
      // 如果是云函数返回的错误，直接使用 message 信息
      errorMessage = error.cloudFunctionResult.message || errorMessage;

      console.log("云函数错误详情:", {
        message: errorMessage,
        error: error.cloudFunctionResult.error,
        data: error.cloudFunctionResult.data
      });
    } else if (error.message) {
      // 如果是其他类型的错误，使用错误消息
      errorMessage = error.message;
    } else {
      // 使用默认的平台相关错误消息
      const defaultErrorMessages = {
        douyin: "抖音视频解析失败，请检查分享链接是否正确或稍后重试",
        xiaohongshu: "小红书视频解析失败，请确认链接有效或检查网络连接",
      };
      errorMessage = defaultErrorMessages[platform] || errorMessage;
    }

    // 直接发送错误信息
    emit("error", errorMessage);
  } finally {
    isProcessing.value = false;
    processingStatus.value = "";
    currentStepIndex.value = 0;
  }
};

// 确认使用解析的视频
const confirmParsedVideo = () => {
  if (parseResult.value) {
    emit("parsed", parseResult.value);
  }
};

// 处理封面加载错误
const handleCoverError = () => {
  // 封面加载失败的静默处理
};

// 辅助函数
const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// 监听解析结果变化
watch(parseResult, (newResult) => {
  // 解析结果变化的处理
});
</script>

<style scoped>
.video-link-parser {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f3f4f6;
}

/* ==================== 头部区域 ==================== */
.parser-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.parser-icon {
  font-size: 40rpx;
  width: 56rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
}

.parser-info {
  flex: 1;
}

.parser-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.parser-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

/* ==================== 输入区域 ==================== */
.input-section {
  margin-bottom: 24rpx;
}

.input-container {
  position: relative;
  margin-bottom: 16rpx;
}

.link-input {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 48rpx 16rpx 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
}

.link-input:focus {
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3rpx rgba(59, 130, 246, 0.1);
}

.link-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.clear-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:active {
  background: #374151;
  transform: scale(0.95);
}

.clear-icon {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

.input-tips {
  background: #f0f9ff;
  border: 1rpx solid #e0f2fe;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
}

.tip-text {
  display: block;
  font-size: 24rpx;
  color: #0369a1;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.tip-item {
  display: block;
  font-size: 22rpx;
  color: #0284c7;
  line-height: 1.4;
  margin-bottom: 4rpx;
}

/* ==================== 操作区域 ==================== */
.action-section {
  margin-bottom: 24rpx;
}

.parse-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.parse-btn:not(.btn-disabled):active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(99, 102, 241, 0.4);
}

.parse-btn.btn-disabled {
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
  cursor: not-allowed;
}

.parse-btn.btn-loading {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.3);
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.loading-icon {
  animation: rotate 2s linear infinite;
}

.btn-text {
  color: inherit;
}

.loading-content .btn-text {
  color: rgba(255, 255, 255, 0.8);
}

/* ==================== 进度区域 ==================== */
.progress-section {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
}

.progress-steps {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.step-indicator {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.step-completed .step-indicator {
  background: #10b981;
  color: white;
}

.step-active .step-indicator {
  background: #f59e0b;
  color: white;
}

.step-pending .step-indicator {
  background: #e5e7eb;
  color: #6b7280;
}

.step-loading {
  animation: rotate 1s linear infinite;
}

.step-text {
  font-size: 26rpx;
  color: #1f2937;
}

.step-active .step-text {
  font-weight: 600;
  color: #f59e0b;
}

.step-completed .step-text {
  color: #10b981;
}

/* ==================== 结果区域 ==================== */
.result-section {
  border: 2rpx solid #10b981;
  border-radius: 16rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
  box-shadow: 0 4rpx 20rpx rgba(16, 185, 129, 0.1);
}

.result-header {
  text-align: center;
  margin-bottom: 24rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(16, 185, 129, 0.2);
}

.result-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #10b981;
  margin-bottom: 8rpx;
}

.result-desc {
  display: block;
  font-size: 26rpx;
  color: #047857;
}

/* 视频预览卡片布局 */
.video-preview {
  margin-bottom: 24rpx;
}

.preview-card {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 1rpx solid rgba(16, 185, 129, 0.2);
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 20rpx;
}

.preview-left {
  flex-shrink: 0;
}

.video-cover {
  position: relative;
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  overflow: hidden;
  background: #f5f5f5;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.cover-image {
  width: 100%;
  height: 100%;
  border-radius: 8rpx;
}

.cover-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.video-cover:active .cover-overlay {
  opacity: 1;
}

.play-icon {
  color: white;
  font-size: 32rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.5);
}

.video-cover-placeholder {
  width: 160rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background: #f3f4f6;
  border: 2rpx dashed #d1d5db;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.placeholder-icon {
  font-size: 28rpx;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 20rpx;
  color: #9ca3af;
}

.preview-right {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.video-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.info-title {
  flex: 1;
}

.title-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.info-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-icon {
  font-size: 20rpx;
  width: 24rpx;
  text-align: center;
}

.meta-text {
  font-size: 24rpx;
  color: #6b7280;
  flex: 1;
}

.meta-badges {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.platform-badge {
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.platform-badge.douyin {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.platform-badge.xiaohongshu {
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
}

.platform-text {
  font-size: 20rpx;
  font-weight: 600;
  color: white;
  line-height: 1;
}

.quality-tag {
  padding: 6rpx 12rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.quality-text {
  font-size: 20rpx;
  font-weight: 600;
  color: white;
  line-height: 1;
}

.result-actions {
  display: flex;
  gap: 12rpx;
}

.retry-btn {
  flex: 1;
  height: 72rpx;
  background: white;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.retry-btn:active {
  background: #f9fafb;
  border-color: #d1d5db;
}

.confirm-btn {
  flex: 2;
  height: 72rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.3);
}

.confirm-btn:active {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.4);
}

/* ==================== 动画效果 ==================== */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
