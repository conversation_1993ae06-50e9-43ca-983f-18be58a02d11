# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a WeChat Mini Program for AI-powered video subtitle generation called "智能字幕胶囊" (Smart Subtitle Capsule). It's built with uni-app + Vue 3 + TypeScript and uses uniCloud (Alibaba Cloud) as the backend service.

The project consists of:
- A complete user interface with WeChat login system
- A comprehensive cloud function backend for video processing
- A multi-step video processing pipeline: video parsing/upload → audio extraction → speech recognition → translation → subtitle synthesis

## Development Commands

### Core Development
```bash
# Start WeChat Mini Program development
npm run dev:mp-weixin

# Build for WeChat Mini Program production
npm run build:mp-weixin

# Type checking
npm run type-check
```

### Other Platform Support
The project supports multiple platforms (H5, Alipay, Baidu, etc.) via uni-app. See package.json for all available dev/build commands.

## Architecture Overview

### Frontend Structure
- **Pages**: `/src/pages/` - Core application pages (index, upload, process, result, history, profile)
- **Components**: `/src/components/` - Reusable UI components including video parser, language selectors
- **Utils**: `/src/utils/api.ts` - Cloud function call wrapper and WeChat authentication
- **Types**: `/src/types/video.ts` - TypeScript definitions for video processing tasks

### Backend Cloud Functions (`/uniCloud-aliyun/cloudfunctions/`)

#### User Management
- `get-wechat-openid` - WeChat authentication and user creation
- `update-user-info` - User profile management
- `get-user-*` - Various user data retrieval functions

#### Video Processing Pipeline
- `parse-video-link` - Extract video from social media links (Douyin, Xiaohongshu)
- `get-oss-upload-policy` - Generate Alibaba Cloud OSS upload credentials
- `process-video-task` - Main task processor and status manager
- `speech-recognition-whisper` - Audio-to-text conversion
- `subtitle-translation-gpt` - Translation service integration
- `download-parsed-video` - Handle video downloads from parsed links

#### Task Management
- `get-task-status` / `get-task-result` - Task monitoring
- `update-task-status` / `cancel-task` / `retry-task` - Task control
- `task-scheduler` - Background task scheduling
- `poll-mps-tasks` - Alibaba Cloud MPS job monitoring

#### Utilities
- `cleanup-old-data` - Data maintenance
- `get-download-url` - Generate secure download URLs

### Database Schema (`/uniCloud-aliyun/database/`)
- **users**: User profiles with WeChat authentication data
- **tasks**: Comprehensive video processing task records with detailed progress tracking

## Key Configuration Files

### Service Configuration
Configuration is centralized in `/src/uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center/`:
- `wechat-mp/config.json` - WeChat Mini Program credentials
- `aliyun-vod/config.json` - Alibaba Cloud VOD service
- `aliyun-oss/config.json` - Object Storage Service
- `aliyun-nls/config.json` - Speech recognition service
- `openai-api/config.json` - Translation service

### Project Files
- `src/manifest.json` - uni-app configuration including WeChat AppID
- `uniCloud.config.json` - Cloud service space configuration
- `tsconfig.json` - TypeScript configuration with path aliases (`@/*` → `src/*`)

## Development Patterns

### Cloud Function Calls
Use the wrapper in `src/utils/api.ts`:
```typescript
import { callCloudFunction } from '@/utils/api'
const result = await callCloudFunction('function-name', { data })
```

### Task Status Management
Video processing follows this flow:
`uploading` → `downloading` → `extracting_audio` → `speech_recognition` → `translating` → `video_synthesis` → `completed`

Each status has corresponding progress tracking in the database schema.

### Error Handling
- Cloud functions return standardized responses with `code`, `message`, `data`, `error` fields
- Frontend components use try-catch with user-friendly error messages
- Task failures are logged with retry mechanisms

## Service Integrations

- **Alibaba Cloud MPS**: Audio extraction and subtitle burning
- **Alibaba Cloud NLS**: Speech recognition (Paraformer model)
- **OpenAI API**: Text translation services
- **Alibaba Cloud OSS**: File storage and CDN
- **WeChat API**: User authentication and authorization

## Important Notes

- The project is currently in development - user interface is complete but video processing backend is being refined
- Uses uniCloud for serverless deployment on Alibaba Cloud
- Supports both local video upload and social media link parsing
- All file operations use Alibaba Cloud OSS with secure upload policies
- Task processing includes comprehensive retry and error recovery mechanisms